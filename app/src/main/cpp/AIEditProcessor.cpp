//
// Created by lapio on 25-1-21.
//

#include "AIEditProcessor.h"

std::vector<std::pair<int,int>> AIEditProcessor::getList()
{
    int res= groupFrames(_frames,_results);
    if(res==-1 || _results.empty()){
        std::cout<<"The results is empty"<<std::endl;
        return {};
    }
    return _results;
}
int AIEditProcessor::PushYoloResult(const std::vector<YOLODetectionResult>& yoloResults,int frame)
{
    return UpdateValidFrames(yoloResults,frame);
}
std::vector<std::pair<int,int>> AIEditProcessor::getFramesTest()
{
    return _Testframes;
}
//return 1 means this frame has a valid shot; -1 means this frame doesnt
int AIEditProcessor::UpdateValidFrames(const std::vector<YOLODetectionResult>& yoloResults,int frame) {
    _balls.clear();
    _boards.clear();
    for (const auto& yoloR: yoloResults){
        if(yoloR.label==1){
            _balls.push_back(yoloR);
        }
        if(yoloR.label==2){
            _boards.push_back(yoloR);
        }
    }
    YOLODetectionResult largest_board;
    int findlargestflag=findLargestArea(_boards,largest_board);
    if (findlargestflag==-1){
        _frames.push_back(std::make_pair(frame, -1));
        return -1;
    }
    double mindist=9999.0;
    for (const auto& ball: _balls){
        double dist=calculateDistance(largest_board,ball);
        //This is for test
        if (dist<mindist) mindist=dist;
        if (dist<_mindistance && ball.height*ball.width < 0.085*(largest_board.height*largest_board.width))
        {
            _frames.push_back(std::make_pair(frame, 1));
            _Testframes.push_back(std::make_pair(frame, (int)dist));
            return 1;
        }
    }
    _frames.push_back(std::make_pair(frame, -1));
    _Testframes.push_back(std::make_pair(frame, (int)mindist));
    return -1;
}

void AIEditProcessor::clear(){
    _results.clear();
}
void AIEditProcessor::init(int type, double mindistance, int backward, int forward, int max_interval, int final_interval) {
    // 初始化类的成员变量
    _type = type;  // 设定处理的类型：0是10帧精准，1是60帧快速
    _mindistance = mindistance;  // 设置最小距离，依照实际需求调整
    _backward = backward;  // 设置往前看
    _forward = forward;    // 设置往后看
    _max_interval = max_interval;  // 设置帧间最大间隔
    _final_interval = final_interval;  // 设置最终间隔

    // 清空帧数据
    _frames.clear();
    _results.clear();
    _balls.clear();
    _boards.clear();
}

//findLargestArea
//返回-1 或 1； -1 表示寻找失败， 1 表示寻找成功
int AIEditProcessor::findLargestArea(const std::vector<YOLODetectionResult>& yolos,YOLODetectionResult& largest) {
    if (yolos.empty()) {
        std::cout << "The vector is empty." << std::endl;
        return -1;
    }
    float maxArea = yolos[0].width * yolos[0].height;
    largest = yolos[0];
    for (const auto& item : yolos) {
        float currentArea = item.width * item.height;
        if (currentArea > maxArea) {
            maxArea = currentArea;
            largest = item;
        }
    }
    return 1;
}

double AIEditProcessor::calculateDistance(const YOLODetectionResult& yolo1, const YOLODetectionResult& yolo2)
{
    float x1 = yolo1.x+yolo1.width/2;
    float y1 = yolo1.y+yolo1.height/2;
    float x2 = yolo2.x+yolo2.width/2;
    float y2 = yolo2.y+yolo2.height/2;
    return std::sqrt(std::pow(x1 - x2, 2) + std::pow(y1 - y2, 2));
}



int AIEditProcessor::groupFrames(const std::vector<std::pair<int,int>>& frames,std::vector<std::pair<int,int>>& results)
{
    if (frames.empty()) {
        return -1;
    }
    //处理有效的
    std::vector<int> validFrames;
    for (const auto& frame : frames) {
        if (frame.second == 1) {  // 仅选取有效帧（第二个值为0表示有效）
            validFrames.push_back(frame.first);
        }
    }
    //如果没有有效帧，返回-1
    if (validFrames.empty()) {
        return -1;
    }
    std::vector<std::pair<int, int>> segments;
    int start = validFrames[0];
    int end = validFrames[0];
    for (int i = 1; i < validFrames.size(); i++) {
        // 如果帧之间的间隔小于等于 max_interval，则将帧合并到当前段
        if (validFrames[i] - end <= _max_interval) {
            end = validFrames[i];
        } else {
            segments.push_back(std::make_pair(start, end));
            start = validFrames[i];
            end = validFrames[i];
        }
    }
    segments.push_back(std::make_pair(start, end));

    //去除掉只包含一个frame的results
    std::vector<std::pair<int, int>> filteredSegments;
    if (_type==1)
    {
        filteredSegments=segments;
    }
    else
    {
        for (const auto& segment : segments) {
            if (segment.first != segment.second) {
                filteredSegments.push_back(segment);
            }
        }
    }

    if (filteredSegments.empty()){
        return -1;
    }
    results.clear();
    std::vector<std::pair<int, int>> expandedSegments;
    for (const auto& segment : filteredSegments) {
        int newStart = std::max(segment.first - _backward, 0);
        int newEnd = std::min(segment.second + _forward,_frames[_frames.size() - 1].first);
        expandedSegments.push_back(std::make_pair(newStart, newEnd));
    }
    std::vector<std::pair<int, int>> mergedSegments;
    mergedSegments.push_back(expandedSegments[0]);
    for (int i = 1; i < expandedSegments.size(); ++i) {
        // 获取上一个合并段
        auto& lastSegment = mergedSegments.back();
        const auto& currentSegment = expandedSegments[i];
        //这里我们进行一个判断是否两段需要被合起来
        if (currentSegment.first <= lastSegment.second + _final_interval) {
            lastSegment.second = currentSegment.second;
        } else {
            mergedSegments.push_back(currentSegment);
        }
    }
    results=mergedSegments;
    return 1;
}