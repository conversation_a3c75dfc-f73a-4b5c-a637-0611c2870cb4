#include "Module/ByteTacker.h"

/**
 * @brief 构造函数，初始化BYTETracker
 *
 * @param frame_rate 帧率，默认为30
 * @param track_buffer 跟踪缓冲区大小，默认为30
 * @param track_thresh 跟踪阈值，默认为0.5
 * @param high_thresh 高阈值，默认为0.6
 * @param match_thresh 匹配阈值，默认为0.8
 */
ByteTacker::ByteTacker(const int &frame_rate, const int &track_buffer, const float &track_thresh, const float &high_thresh, const float &match_thresh)
    : byte(new byte_track::BYTETracker(frame_rate, track_buffer, track_thresh, high_thresh, match_thresh)), frame_rate_(frame_rate), track_buffer_(track_buffer), track_thresh_(track_thresh), high_thresh_(high_thresh), match_thresh_(match_thresh) {}

/**
 * @brief 初始化ByteTacker单目标追踪器，确定追踪目标
 *
 * @param yolo_detection_results 第一帧图片的YOLO检测结果(所有结果的置信度以及坐标信息)
 * @param index 追踪目标的YOLO检测结果索引(即追踪目标是YOLO检测结果中的第几个)
 */
void ByteTacker::targetInit(std::vector<YOLODetectionResult> &yolo_detection_results, int index)
{
    delete byte;
    this->byte = new byte_track::BYTETracker(frame_rate_, track_buffer_, track_thresh_, high_thresh_, match_thresh_);
    for (auto &det : yolo_detection_results)
    {
        det.prob = 1.0;
    }
    std::vector<byte_track::Object> objects = convertDetections2Objects(yolo_detection_results);
    const auto outputs = byte->update(objects);
    target_id_ = outputs[index]->getTrackId();
}

/**
 * @brief 目标追踪函数：利用ByteTrack实现的单目标追踪
 *
 * @param yolo_detection_results 当前帧图片的YOLO检测结果(所有结果的置信度以及坐标信息)
 * @return int 跟踪结果的索引，如果未找到匹配的目标则返回-1
 */
int ByteTacker::track(const std::vector<YOLODetectionResult> &yolo_detection_results)
{
    std::vector<byte_track::Object> objects = convertDetections2Objects(yolo_detection_results);
    const auto outputs = byte->update(objects);
    for (const auto &output : outputs)
    {
        if (target_id_ == output->getTrackId())
        {
            std::vector<double> det_distance;
            for (const auto &det : yolo_detection_results)
            {
                det_distance.push_back(pow(det.x - output->getRect().x(), 2) + pow(det.y - output->getRect().y(), 2));
            }
            int min_index = std::min_element(det_distance.begin(), det_distance.end()) - det_distance.begin();
            return min_index;
        }
    }
    return -1;
}

/**
 * @brief 清空ByteTrack原有记录的信息，初始化ByteTacker单目标追踪器，确定追踪目标
 *
 * @param yolo_detection_results 新一帧图片的YOLO检测结果(所有结果的置信度以及坐标信息)
 * @param index 追踪目标的YOLO检测结果索引(即追踪目标是YOLO检测结果中的第几个)
 */
void ByteTacker::restart(std::vector<YOLODetectionResult> &yolo_detection_results, int index)
{
    targetInit(yolo_detection_results, index);
}

/**
 * @brief 将YOLO检测结果转换为ByteTrack所需的Object格式
 *
 * @param yolo_detection_results YOLO检测结果
 * @return std::vector<byte_track::Object> ByteTrack所需的Object格式
 */
std::vector<byte_track::Object> convertDetections2Objects(const std::vector<YOLODetectionResult> &yolo_detection_results)
{
    std::vector<byte_track::Object> objects;
    for (const auto &det : yolo_detection_results)
    {
        byte_track::Rect<float> rect(det.x, det.y, det.width, det.height);
        byte_track::Object obj(rect, 0, det.prob);

        objects.push_back(obj);
    }
    return objects;
}