<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <androidx.appcompat.widget.LinearLayoutCompat
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_gimbal_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="21dp"
            android:layout_marginTop="17dp"
            android:drawableLeft="@drawable/ic_gimble"
            android:gravity="center_vertical"
            android:text="@string/gimbalDevice"
            android:textColor="@color/black"
            android:textSize="13dp"
            android:textStyle="bold"
            android:visibility="gone" />

        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="18dp"
            android:layout_marginTop="11dp"
            android:layout_marginEnd="18dp"
            app:cardBackgroundColor="#efefef"
            app:cardCornerRadius="14dp"
            app:cardElevation="8dp">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_device_gimbal_gen1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                tools:itemCount="2"
                tools:listitem="@layout/item_device" />

        </androidx.cardview.widget.CardView>

        <View
            android:id="@+id/divider"
            android:layout_width="match_parent"
            android:layout_height="4dp"
            android:layout_marginTop="30dp"
            android:background="#f3f3f3" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_gen2gimbal_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="21dp"
            android:layout_marginTop="17dp"
            android:drawableLeft="@drawable/ic_chameleon"
            android:drawablePadding="5dp"
            android:gravity="center_vertical"
            android:text="@string/xbot2"
            android:textColor="@color/black"
            android:textSize="13dp"
            android:textStyle="bold"
            android:visibility="gone" />

        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="18dp"
            android:layout_marginTop="9dp"
            android:layout_marginEnd="18dp"
            android:layout_marginBottom="10dp"
            app:cardBackgroundColor="#efefef"
            app:cardCornerRadius="14dp"
            app:cardElevation="8dp">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_device_gimbal_chameleon"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                tools:itemCount="2"
                tools:listitem="@layout/item_device" />
        </androidx.cardview.widget.CardView>
    </androidx.appcompat.widget.LinearLayoutCompat>
</androidx.core.widget.NestedScrollView>


