<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"

    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="327dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal">

        <View
            android:id="@+id/view8"
            android:layout_width="match_parent"
            android:layout_height="163dp"
            android:layout_marginTop="65dp"
            android:layout_marginRight="12dp"
            android:background="@drawable/bg_broadcast_title"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:id="@+id/view9"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginTop="65dp"
            android:background="@drawable/bg_broadcast_right"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/imageView6"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:srcCompat="@drawable/bg_effect_light" />

        <ImageView
            android:id="@+id/imageView7"
            android:layout_width="228dp"
            android:layout_height="183dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:srcCompat="@drawable/ic_thumbs_up" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="162dp"
            android:background="@drawable/shape_dialog_broadcast"
            android:minHeight="252dp"
            android:orientation="vertical"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:gravity="center"
                android:text="@string/in_app_review_dialog_title"
                android:textColor="@color/black"
                android:textSize="16dp" />

            <TextView
                android:id="@+id/tv_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:gravity="center"
                android:text="@string/in_app_review_dialog_content"
                android:textColor="@color/tv_gray"
                android:textSize="12dp" />

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/bt_confirm"
                style="@style/Widget.AppCompat.Button"
                android:layout_width="196dp"
                android:layout_height="40dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="24dp"
                android:background="@drawable/bg_button_comfirm"
                android:text="@string/in_app_review_dialog_cfm_button"
                android:textAllCaps="false"
                android:textColor="@color/white"
                android:textSize="15dp" />

            <TextView
                android:id="@+id/tv_cancel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:gravity="center"
                android:layout_gravity="center"
                android:text="@string/in_app_review_dialog_cancal_button"
                android:textColor="@color/text_gray_606060"
                android:textSize="14dp" />


        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>


</FrameLayout>