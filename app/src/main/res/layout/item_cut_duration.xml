<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_cut_duration"
        android:layout_width="match_parent"
        android:layout_height="34dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginStart="5dp"
        android:layout_marginEnd="5dp"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:maxLines="1"
        android:gravity="center"
        android:textColor="@color/text_gray"
        android:textStyle="bold"
        android:background="@drawable/bg_gray_conner_20dp"
        />
</androidx.constraintlayout.widget.ConstraintLayout>