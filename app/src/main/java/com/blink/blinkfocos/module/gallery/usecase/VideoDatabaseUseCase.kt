package com.blink.blinkfocos.module.gallery.usecase

import com.blink.blinkfocos.db.FolderDao
import com.blink.blinkfocos.db.VideoDao
import com.blink.blinkfocos.entity.FolderDbEntity
import com.blink.blinkfocos.entity.VideoCloudItemEntity
import com.blink.blinkfocos.entity.VideoDbEntity
import com.blink.blinkfocos.ext.extractDateFromUrl
import com.blink.blinkfocos.module.gallery.constant.GalleryRvCheckState
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/***
 * fun [loadAllFromDB]: 从数据库加载所有视频和文件夹数据，并按日期排序
 * fun [getDbCount]: 获取数据库中视频和文件夹的总数量
 * fun [getDateFromObject]: 从对象中提取日期字符串
 ***/
object VideoDatabaseUseCase {
    suspend fun loadAllFromDB(
        videoDao: VideoDao,
        folderDao: FolderDao,
        checkState: GalleryRvCheckState?,
        videoCheckList: ArrayList<VideoCloudItemEntity>?
    ): Triple<ArrayList<VideoCloudItemEntity>, ArrayList<VideoCloudItemEntity>, ArrayList<VideoCloudItemEntity>> {
        return withContext(Dispatchers.IO) {
            val videoDbEntity = videoDao.allVideosSortedByDateDescending
            val folderDbEntity = folderDao.dbFolders.filter { it.count > 0 }

            val mergedList = (videoDbEntity + folderDbEntity).sortedByDescending {
                getDateFromObject(it)
            }

            //return list
            val allList = arrayListOf<VideoCloudItemEntity>()
            val markedList = arrayListOf<VideoCloudItemEntity>()
            val editedList = arrayListOf<VideoCloudItemEntity>()

            var currentDate = ""
            val currentDateAll = arrayListOf<VideoCloudItemEntity>()
            val currentDateMarked = arrayListOf<VideoCloudItemEntity>()
            val currentDateEdited = arrayListOf<VideoCloudItemEntity>()

            for (item in mergedList) {
                val itemDate = getDateFromObject(item)
                if (itemDate != currentDate) {
                    currentDate = itemDate
                    //为什么是1: 由于每一次检测到新的日期我们都会添加一次TITLE 进去list里面，所以如果size ==1 的话
                    //就代表是只有header而里面没有任何东西，所以我们就不需要把当天的这个列表添加到总列表里面了
                    if (currentDateAll.size > 1) {
                        allList.addAll(currentDateAll)
                    }

                    if (currentDateMarked.size > 1) {
                        markedList.addAll(currentDateMarked)
                    }
                    if (currentDateEdited.size > 1) {
                        editedList.addAll(currentDateEdited)
                    }

                    currentDateAll.clear()
                    currentDateMarked.clear()
                    currentDateEdited.clear()
                    //把上一天的数据写到总列表里面，清除当天的三个列表
                    //接下来可以开始新的一天的循环
                    currentDateAll.add(VideoCloudItemEntity(itemDate, VideoCloudItemEntity.TITLE))
                    currentDateMarked.add(
                        VideoCloudItemEntity(
                            itemDate, VideoCloudItemEntity.TITLE
                        )
                    )
                    currentDateEdited.add(
                        VideoCloudItemEntity(
                            itemDate, VideoCloudItemEntity.TITLE
                        )
                    )
                }

                if (item is VideoDbEntity) {
                    val videoItem = VideoCloudItemEntity(
                        item.name,
                        item.url,
                        item.time,
                        item.base64,
                        item.isUpload,
                        item.address,
                        item.recordTime,
                        item.logAddress,
                        item.modelType,
                        item.mobileVision,
                        item.systemVision,
                        item.appVison,
                        VideoCloudItemEntity.VIDEO,
                        item.dottedAddress,
                        item.isCut
                    )
                    if (checkState == GalleryRvCheckState.Checkable) {
                        videoItem.checkType = VideoCloudItemEntity.UNCHECK
                        if (videoCheckList?.size != 0) {
                            for (checkVideo in videoCheckList ?: arrayListOf()) {
                                if (checkVideo.url == videoItem.url) {
                                    videoItem.checkType = VideoCloudItemEntity.CHECKED
                                }
                            }
                        }
                    }
                    if (videoItem.clickUrl != null && videoItem.clickUrl.isNotBlank()) {
                        currentDateMarked += videoItem
                    }
                    if (videoItem.isClip != null && videoItem.isClip == "1") {
                        currentDateEdited += videoItem
                    }
                    currentDateAll += videoItem
                } else if (item is FolderDbEntity) {
                    currentDateAll += VideoCloudItemEntity(
                        item.count,
                        item.base64,
                        VideoCloudItemEntity.FOLDER,
                        item.createTime,
                        item.folderUUID
                    )
                }
            }
            //由于forloop 运行结束后currentdate list 会存在最后一天的数据，所以在最后需要把最后一天数据添加到list中返回
            if (currentDateAll.size > 1) {
                allList.addAll(currentDateAll)
            }
            if (currentDateMarked.size > 1) {
                markedList.addAll(currentDateMarked)
            }
            if (currentDateEdited.size > 1) {
                editedList.addAll(currentDateEdited)
            }

            Triple(allList, markedList, editedList)
        }
    }

    suspend fun getDbCount(
        videoDao: VideoDao,
        folderDao: FolderDao,
    ): Int {
        val itemCount = withContext(Dispatchers.IO) {
            val videoDbEntityCount =
                videoDao.allVideosCount
            val folderDbEntityCount =
                folderDao.dbFoldersCount
            videoDbEntityCount + folderDbEntityCount
        }
        return itemCount
    }

    private fun getDateFromObject(obj: Any): String {
        // 实现从对象中提取日期的方法
        return when (obj) {
            is VideoDbEntity -> obj.url.extractDateFromUrl()
            is FolderDbEntity -> obj.createTime
            else -> ""
        }
    }
}