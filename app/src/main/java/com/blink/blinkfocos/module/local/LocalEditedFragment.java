package com.blink.blinkfocos.module.local;

import static com.blink.blinkfocos.widget.MyDialog.DOWNLOAD_LOW_STORAGE;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.content.ContentValues;
import android.content.Context;
import android.content.Intent;
import android.content.res.Configuration;
import android.graphics.Bitmap;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.os.Bundle;
import android.os.Environment;
import android.os.StatFs;
import android.provider.MediaStore;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.aries.library.fast.i.IFastRefreshView;
import com.aries.library.fast.module.fragment.FastTitleFragment;
import com.aries.library.fast.retrofit.FastLoadingObserver;
import com.aries.library.fast.retrofit.FastObserver;
import com.aries.library.fast.util.FastUtil;
import com.aries.library.fast.util.ToastUtil;
import com.aries.ui.view.title.TitleBarView;
import com.blink.blinkfocos.App;
import com.blink.blinkfocos.R;
import com.blink.blinkfocos.adapter.VideoLocalAdapter;
import com.blink.libshare.GlobalConstant;
import com.blink.blinkfocos.db.VideoDbSingletion;
import com.blink.blinkfocos.entity.LocalFileRefreshEvent;
import com.blink.blinkfocos.entity.UserInfoEntity;
import com.blink.blinkfocos.entity.VideoCloudItemEntity;
import com.blink.blinkfocos.entity.VideoDbEntity;
import com.blink.blinkfocos.entity.VideoLoadEntity;
import com.blink.blinkfocos.ext.StringExtKt;
import com.blink.blinkfocos.module.gallery.di.GalleryMenuInterface;
import com.blink.blinkfocos.module.login.LoginPasswordActivity;
import com.blink.blinkfocos.module.share.ShareActivity;
import com.blink.blinkfocos.retrofit.repository.ApiRepository;
import com.blink.blinkfocos.updload.Uploader;
import com.blink.blinkfocos.util.FileUtils;
import com.blink.libshare.MobclickAgentUtil;
import com.blink.blinkfocos.util.MySetting;
import com.blink.blinkfocos.widget.MyDialog;
import com.bumptech.glide.Glide;
import com.bumptech.glide.request.target.CustomTarget;
import com.bumptech.glide.request.transition.Transition;
import com.luck.picture.lib.basic.PictureSelectionSystemModel;
import com.luck.picture.lib.basic.PictureSelector;
import com.luck.picture.lib.config.PictureMimeType;
import com.luck.picture.lib.config.SelectMimeType;
import com.luck.picture.lib.config.SelectModeConfig;
import com.luck.picture.lib.dialog.PictureLoadingDialog;
import com.luck.picture.lib.engine.UriToFileTransformEngine;
import com.luck.picture.lib.entity.LocalMedia;
import com.luck.picture.lib.entity.MediaExtraInfo;
import com.luck.picture.lib.interfaces.OnKeyValueResultCallbackListener;
import com.luck.picture.lib.interfaces.OnResultCallbackListener;
import com.luck.picture.lib.interfaces.OnVideoThumbnailEventListener;
import com.luck.picture.lib.utils.MediaUtils;
import com.luck.picture.lib.utils.PictureFileUtils;
import com.luck.picture.lib.utils.SandboxTransformUtils;
import com.scwang.smart.refresh.layout.SmartRefreshLayout;
import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.trello.rxlifecycle3.android.FragmentEvent;

import org.greenrobot.eventbus.EventBus;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoField;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import io.reactivex.Observable;
import io.reactivex.ObservableOnSubscribe;
import io.reactivex.Observer;
import io.reactivex.SingleObserver;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;

public class LocalEditedFragment extends FastTitleFragment implements IFastRefreshView, GalleryMenuInterface {
    private LocalEditedFragment fragment;
    private VideoLocalAdapter mAdapter;
    private View empty_layout;
    private List<VideoCloudItemEntity> videoList = new ArrayList<>();
    private List<VideoCloudItemEntity> checkVideoList = new ArrayList<>();
    private SmartRefreshLayout mRefreshLayout;
    private RecyclerView mRecyclerView;

    //不可选
    public static final int UNCHECKABLE = 0;
    //可选
    public static final int CHECKABLE = 1;
    private int checkState = UNCHECKABLE;
    private LocalFileFragment parentFragment;
    private MyDialog addAlbumDialog;
    private MyDialog deleteVideoDialog;
    private MyDialog multiSelectShareDialog;
    private Disposable subscribe;
    private Dialog mLoadingDialog;
    private AppCompatImageView iv_empty_live_list;
    private boolean isLoading;
    private int mSpanSize = 3;
    private MyDialog logigDialog;
    private MyDialog lowStorageDialog;

    public LocalEditedFragment newInstance() {
        Bundle args = new Bundle();
        if (fragment == null) {
            fragment = new LocalEditedFragment();
            fragment.setArguments(args);
        }

        return fragment;
    }


    @Override
    public int getContentLayout() {
        return R.layout.fragment_cloud_item;
    }

    @Override
    public void setRefreshLayout(SmartRefreshLayout refreshLayout) {
        mRefreshLayout = refreshLayout;
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        mSpanSize = getResources().getConfiguration().orientation == Configuration.ORIENTATION_LANDSCAPE ? 6 : 3;
        setRetainInstance(true);
        empty_layout = mContentView.findViewById(R.id.layout_live_list_empty);
        mRecyclerView = mContentView.findViewById(R.id.rv_contentFastLib);
        iv_empty_live_list = mContentView.findViewById(R.id.iv_empty_live_list);
        if (App.language.equals("zh")) {
            iv_empty_live_list.setImageResource(R.drawable.ic_empty_cloud_list);
        } else if (App.language.equals("ja")) {
            iv_empty_live_list.setImageResource(R.drawable.ic_empty_cloud_list_ja);
        } else {
            iv_empty_live_list.setImageResource(R.drawable.ic_empty_cloud_list_en);
        }

        logigDialog = new MyDialog(mContext, R.string.confirm, R.string.please_sign_when_share, MyDialog.CONTENT);
        logigDialog.setCallback(() -> FastUtil.startActivity(mContext, LoginPasswordActivity.class));
        parentFragment = (LocalFileFragment) getParentFragment();
        checkState = UNCHECKABLE;
        initRecyclerView();
        initDialog();

    }

    private void initDialog() {
        addAlbumDialog = new MyDialog(mContext, R.string.confirm, R.string.load_to_album, MyDialog.CONTENT);
        addAlbumDialog.setCallback(this::addLocalToAlbum);
        deleteVideoDialog = new MyDialog(mContext, R.string.confirm, R.string.delete_checked_video, MyDialog.CONTENT);
        deleteVideoDialog.setCallback(this::deleteVideo);
        lowStorageDialog = new MyDialog(mContext, R.string.empty, R.string.download_low_storage, DOWNLOAD_LOW_STORAGE);
    }


    @Override
    public void loadData() {
        super.loadData();
        if (mRefreshLayout != null) {
            mRefreshLayout.autoRefresh();
        }
    }

    @Override
    public void loadVideo() {
        if (mRefreshLayout != null) {
            mRefreshLayout.autoRefresh();
        }
    }

    @Override
    public void onRefresh(@NonNull RefreshLayout refreshLayout) {
//        if (checkState == UNCHECKABLE) {
//
//        } else {
//            mRefreshLayout.finishRefresh();
//        }
        if (isLoading) {
            return;
        }
        load();
    }

    String currentDate = null;

    private void load() {
        isLoading = true;
        currentDate = null;
        if (videoList != null) {
            videoList.clear();
        } else {
            videoList = new ArrayList<>();
        }
        Observable.fromCallable(() -> App.getVideoDatabase().videoDao().getAllVideosFilteredByIsCut())
                .subscribeOn(Schedulers.io()) // 在 IO 线程执行数据库查询
                .compose(bindUntilEvent(FragmentEvent.DESTROY))
                .flatMapIterable(videoDbEntities -> videoDbEntities)
                .concatMap(data -> {
                    String dataDate = StringExtKt.extractDateFromUrl(data.getUrl());
                    if (dataDate.isEmpty()) {
                        return Observable.empty(); // 跳过处理该数据，返回一个空 Observable
                    }
                    List<VideoCloudItemEntity> entities = new ArrayList<>();
                    if (!dataDate.equals(currentDate)) {
                        entities.add(new VideoCloudItemEntity(dataDate, VideoCloudItemEntity.TITLE));
                        currentDate = dataDate;
                    }
                    entities.add(new VideoCloudItemEntity(data.getName(), data.getUrl(), data.getTime(),
                            data.getBase64(), data.getIsUpload(), data.getAddress(), data.getRecordTime(), data.getLogAddress(),
                            data.getModelType(), data.getMobileVision(), data.getSystemVision(), data.getAppVison(),
                            VideoCloudItemEntity.VIDEO, data.getDottedAddress(), data.getIsCut()));
                    return Observable.fromIterable(entities);
                })
                .toList()
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new SingleObserver<List<VideoCloudItemEntity>>() {
                    @Override
                    public void onSubscribe(Disposable d) {
                        // 订阅时的操作
                    }

                    @Override
                    public void onSuccess(List<VideoCloudItemEntity> list) {
                        isLoading = false;
                        videoList.addAll(list);
                        if (videoList.size() > 0) {
                            videoList.add(new VideoCloudItemEntity(" ", VideoCloudItemEntity.SPACE));
                            mRecyclerView.setVisibility(View.VISIBLE);
                            mAdapter.setList(videoList);
                            empty_layout.setVisibility(View.GONE);
                        } else {
                            empty_layout.setVisibility(View.VISIBLE);
                            mRecyclerView.setVisibility(View.GONE);
                        }
                        if (mRefreshLayout.isRefreshing()) {
                            mRefreshLayout.finishRefresh();
                        }
                    }

                    @Override
                    public void onError(Throwable e) {
                        // 错误处理
                        Log.e(TAG, e.getMessage());
                        ToastUtil.show(e.getMessage());
                        isLoading = false;
                        if (videoList.size() > 0) {
                            videoList.add(new VideoCloudItemEntity(" ", VideoCloudItemEntity.SPACE));
                            mRecyclerView.setVisibility(View.VISIBLE);
                            mAdapter.setList(videoList);
                            empty_layout.setVisibility(View.GONE);
                        } else {
                            empty_layout.setVisibility(View.VISIBLE);
                            mRecyclerView.setVisibility(View.GONE);
                        }
                        if (mRefreshLayout.isRefreshing()) {
                            mRefreshLayout.finishRefresh();
                        }
                    }
                });
//        new Thread(new Runnable() {
//            @Override
//            public void run() {
//                List<VideoDbEntity> dbVideoSs = App.getVideoDatabase().videoDao().getAllVideosSortedByDateDescending();
//                String currentDate = null;
//                for (VideoDbEntity data : dbVideoSs) {
//                    String dataDate = extractDateFromUrl(data.getUrl()); // 提取URL中的日期部分
//                    // 如果日期为空，跳过处理该数据
//                    if (dataDate.isEmpty()) {
//                        continue;
//                    }
//                    if (!dataDate.equals(currentDate)) {
//                        // 添加日期标题项
//                        VideoCloudItemEntity dateItem = new VideoCloudItemEntity(dataDate, VideoCloudItemEntity.TITLE);
//                        videoList.add(dateItem);
//                        currentDate = dataDate; // 更新当前日期
//                    }
//                    VideoCloudItemEntity videoCloudItem = new VideoCloudItemEntity(data.getName()
//                            , data.getUrl(), data.getTime(), data.getBase64(), data.getIsUpload()
//                            , data.getAddress(), data.getRecordTime(), data.getLogAddress(), data.getModelType()
//                            , data.getMobileVision(), data.getSystemVision(), data.getAppVison(), VideoCloudItemEntity.VIDEO);
//                    // 添加视频数据项
//                    videoList.add(videoCloudItem);
//                }
//                mContext.runOnUiThread(new Runnable() {
//                    @Override
//                    public void run() {
//                        if (videoList.size() > 0) {
//                            videoList.add(new VideoCloudItemEntity(" ", VideoCloudItemEntity.SPACE));
//                            mRecyclerView.setVisibility(View.VISIBLE);
//                            mAdapter.setList(videoList);
//                            empty_layout.setVisibility(View.GONE);
//                        } else {
//                            empty_layout.setVisibility(View.VISIBLE);
//                            mRecyclerView.setVisibility(View.GONE);
//                        }
//                        if (mRefreshLayout.isRefreshing()) {
//                            mRefreshLayout.finishRefresh();
//                        }
//                    }
//                });
//
//            }
//        }).start();


    }

    @Override
    public void onDestroy() {
        super.onDestroy();
//        if (addAlbumDialog != null) {
//            addAlbumDialog.destroy();
//        }
//        if (deleteVideoDialog != null) {
//            deleteVideoDialog.destroy();
//        }
//        if (mHandler != null) {
//            mHandler.removeCallbacksAndMessages(null);
//            mHandlerThread.quit();
//            mHandler = null;
//        }
//        if (mHandlerThread != null) {
//            mHandlerThread = null;
//        }
//        if (parentFragment != null) {
//            parentFragment = null;
//        }
        if (multiSelectShareDialog != null) {
            multiSelectShareDialog.destroy();
            multiSelectShareDialog = null;
        }
        if (lowStorageDialog != null) {
            lowStorageDialog.destroy();
            lowStorageDialog = null;
        }
    }

    private void initRecyclerView() {
        mAdapter = new VideoLocalAdapter();
        GridLayoutManager gridLayoutManager = new GridLayoutManager(mContext, mSpanSize);


        mRecyclerView.setLayoutManager(gridLayoutManager);
        //重新定义每行的item数量
        gridLayoutManager.setSpanSizeLookup(new GridLayoutManager.SpanSizeLookup() {
            @Override
            public int getSpanSize(int position) {
                if (videoList.size() <= 1 || position > videoList.size() - 1) {
                    return mSpanSize;
                }
                VideoCloudItemEntity multiPiggy = videoList.get(position);
                int spanSize = 0;
                switch (multiPiggy.getItemType()) {
                    case VideoCloudItemEntity.TITLE:
                    case VideoCloudItemEntity.SPACE:
                        spanSize = mSpanSize;//一行一个
                        break;
                    case VideoCloudItemEntity.VIDEO:
                    case VideoCloudItemEntity.FOLDER:
                        spanSize = 1;//一行三个
                        break;

                }
                return spanSize;
            }
        });
        mAdapter.setOnItemClickListener((adapter, view, position) -> {
            if (videoList == null || videoList.size() <= position) {
                return;
            }
            VideoCloudItemEntity videoDb = videoList.get(position);
            if (videoDb.getFieldType() != VideoCloudItemEntity.VIDEO) {
                return;
            }
            if (checkState == UNCHECKABLE) {
                MobclickAgentUtil.clickEvent(GlobalConstant.KEY_LOCAL, GlobalConstant.VALUE_LOCALPLAYVIDEO);
                Bundle bundle = new Bundle();
                bundle.putString("url", videoDb.getUrl());
                bundle.putString("clickUrl", videoDb.getClickUrl());
                bundle.putString("duration", videoDb.getTime());
                bundle.putString("thumbnail", videoDb.getBase64());
                FastUtil.startActivity(mContext, DirectPlayActivity.class, bundle);
            } else if (checkState == CHECKABLE) {
                if (videoDb.getCheckType() == VideoCloudItemEntity.UNCHECK) {
                    videoDb.setCheckType(VideoCloudItemEntity.CHECKED);
                    checkVideoList.add(videoDb);
                } else if (videoDb.getCheckType() == VideoCloudItemEntity.CHECKED) {
                    videoDb.setCheckType(VideoCloudItemEntity.UNCHECK);
                    checkVideoList.remove(videoDb);
                }
                parentFragment.changeSelectSize(checkVideoList.size());
                adapter.notifyDataSetChanged();
            }
        });


        mRecyclerView.setAdapter(mAdapter);
    }

    @Override
    public void onViewStateRestored(@Nullable Bundle savedInstanceState) {
        super.onViewStateRestored(savedInstanceState);

    }

    @Override
    @SuppressLint("NotifyDataSetChanged")
    public void changeCheckState(int state) {

        try {
            if (state == VideoCloudItemEntity.UNCHECKABLE) {
                checkState = UNCHECKABLE;

            } else if (state == VideoCloudItemEntity.UNCHECK) {
                checkState = CHECKABLE;
                if (checkVideoList != null) {
                    checkVideoList.clear();
                }
            }
            if (mAdapter == null) {
                return;
            }
            if (videoList == null || videoList.size() == 0) {
                mAdapter.notifyDataSetChanged();
            } else {
                for (int i = 0; i < videoList.size(); i++) {
                    if (videoList.get(i) != null) {
                        videoList.get(i).setCheckType(state);
                    }

                }
                mAdapter.notifyDataSetChanged();
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void setTitleBar(TitleBarView titleBar) {

    }


    @Override
    public void download() {
        if (mContext == null || addAlbumDialog == null) {
            return;
        }
        long requiredSpace = 0L;
        for (VideoCloudItemEntity entity : checkVideoList) {
            Long length = new File(entity.getUrl()).length();
            requiredSpace += length;
        }
        if (!isEnoughSpace(requiredSpace)) {
            lowStorageDialog.show();
            return;
        }
        addAlbumDialog.show();
    }

    public boolean isEnoughSpace(long requiredSpace) {
        StatFs statFs = new StatFs(Environment.getExternalStorageDirectory().getPath());
        long blockSize = statFs.getBlockSizeLong();
        long availableBlocks = statFs.getAvailableBlocksLong();
        long availableBytes = blockSize * availableBlocks;
        return availableBytes > requiredSpace;
    }

    private void addLocalToAlbum() {
        MobclickAgentUtil.clickEvent(GlobalConstant.KEY_LOCAL, GlobalConstant.VALUE_SAVEVIDEOTOALBUM);
        if (checkVideoList.size() == 0) {
            return;
        }
        try {
            mLoadingDialog = new PictureLoadingDialog(getContext());
            mLoadingDialog.show();
        } catch (Exception e) {
            e.printStackTrace();
        }
        ArrayList<Observable<Boolean>> observables = new ArrayList<>();
        for (int i = 0; i < checkVideoList.size(); i++) {

            int finalI = i;
            Observable<Boolean> newFile = Observable.create((ObservableOnSubscribe<Boolean>) emitter -> {
                        boolean isWriteFileSuccess = false;
                        try {
                            Uri externalSavedUri;

                            ContentValues contentValues = FileUtils.buildVideoContentValues(checkVideoList.get(finalI).getName());
                            externalSavedUri = mContext.getContentResolver().insert(MediaStore.Video.Media.EXTERNAL_CONTENT_URI, contentValues);
                            if (externalSavedUri == null) {
                                return;
                            }
                            OutputStream outputStream = mContext.getContentResolver().openOutputStream(externalSavedUri);
                            isWriteFileSuccess = FileUtils.writeFileFromIS(new FileInputStream(checkVideoList.get(finalI).getUrl()), outputStream);
                        } catch (FileNotFoundException e) {
                            e.printStackTrace();
                        }
                        emitter.onNext(isWriteFileSuccess);
                        emitter.onComplete();
                    }).subscribeOn(Schedulers.io())//注意：这里指定线程
                    .observeOn(AndroidSchedulers.mainThread());
            ;
            observables.add(newFile);
        }
        Observable<Boolean>[] array = observables.toArray(new Observable[]{});
        Observable.mergeArrayDelayError(array)
                .subscribe(new Observer<Boolean>() {
                    @Override
                    public void onSubscribe(Disposable d) {

                    }

                    @Override
                    public void onNext(Boolean o) {
                        Log.e(TAG, "添加到相册：" + o);
                    }

                    @Override
                    public void onError(Throwable e) {
                        try {
                            if (mLoadingDialog != null && mLoadingDialog.isShowing()) {
                                mLoadingDialog.dismiss();
                            }
                        } catch (Exception exception) {
                            exception.printStackTrace();
                        } finally {
                            mLoadingDialog = null;
                        }
                        ToastUtil.show(e.getMessage());
                        if (getParentFragment() != null) {
                            ((LocalFileFragment) getParentFragment()).changeCheckState(VideoCloudItemEntity.UNCHECKABLE);
                        }
                    }

                    @Override
                    public void onComplete() {
                        try {
                            if (mLoadingDialog != null && mLoadingDialog.isShowing()) {
                                mLoadingDialog.dismiss();
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        } finally {
                            mLoadingDialog = null;
                        }
                        ToastUtil.show(mContext.getText(R.string.save_video_in_photos));

                        if (getParentFragment() != null) {
                            ((LocalFileFragment) getParentFragment()).changeCheckState(VideoCloudItemEntity.UNCHECKABLE);
                        }
                    }
                });
    }


    @Override
    public void uploadOrShare() {


        if (getParentFragment() != null) {
            ((LocalFileFragment) getParentFragment()).changeCheckState(VideoCloudItemEntity.UNCHECKABLE);
        }
        MobclickAgentUtil.clickEvent(GlobalConstant.KEY_LOCAL, GlobalConstant.VALUE_UPLOADVIDEO);
        ApiRepository.getInstance().getUserInfo()
                .compose(bindUntilEvent(FragmentEvent.DESTROY))
                .subscribe(new FastObserver<UserInfoEntity>() {

                    @Override
                    public void _onNext(UserInfoEntity entity) {
                        MySetting.getInstance().setUserInfoEntity(entity);
                        if (!MySetting.getInstance().getUserInfoEntity().isLogin()) {
                            return;
                        }
                        List<VideoCloudItemEntity> list = new ArrayList<>();
                        List<VideoLoadEntity> loadEntityList = Uploader.INSTANCE.getLoadEntityList();
                        for (int i = 0; i < checkVideoList.size(); i++) {
                            if (loadEntityList.isEmpty()) {
                                list.add(checkVideoList.get(i));
                            } else {
                                boolean haveFile = false;
                                for (int j = 0; j < loadEntityList.size(); j++) {
                                    if (loadEntityList.get(j).getName().equals(checkVideoList.get(i).getName())) {
                                        haveFile = true;
                                    }
                                }
                                if (!haveFile) {
                                    list.add(checkVideoList.get(i));
                                }
                            }
                        }
                        Uploader.INSTANCE.addAllUploadItems(list);
                        Bundle bundle = new Bundle();
                        bundle.putInt("position", TransferActivity.UPLOAD);
                        bundle.putInt("type", 1);
                        FastUtil.startActivity(mContext, TransferActivity.class, bundle);

                    }
                });

    }


    @Override
    public void delete() {
        if (mContext == null || deleteVideoDialog == null) {
            return;
        }
        deleteVideoDialog.show();
    }

    private void deleteVideo() {
        MobclickAgentUtil.clickEvent(GlobalConstant.KEY_LOCAL, GlobalConstant.VALUE_DELETELOCALVIDEO);
        Observable.create((ObservableOnSubscribe<Boolean>) emitter -> {


                    for (VideoCloudItemEntity videoCloudItem : checkVideoList) {

                        videoList.removeIf(next -> {
                            if (TextUtils.isEmpty(next.getName())) {
                                return false;
                            }
                            return next.getName().equals(videoCloudItem.getName());
                        });
                        VideoDbSingletion.getInstance().deleteVideoDb(videoCloudItem.getUrl());
                    }

                    emitter.onNext(true);
                    emitter.onComplete();
                }).subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .compose(bindUntilEvent(FragmentEvent.DESTROY))
                .subscribe(new FastLoadingObserver<Boolean>() {
                    @Override
                    public void _onNext(Boolean entity) {
                        Log.i(TAG, "删除成功");
                        checkVideoList.clear();
                        if (getParentFragment() != null) {
                            parentFragment.changeSelectSize(checkVideoList.size());
                            if (videoList.size() == 0) {
                                ((LocalFileFragment) getParentFragment()).changeCheckState(VideoCloudItemEntity.UNCHECKABLE);
                            }

                        }
                        //load();
                        EventBus.getDefault().post(new LocalFileRefreshEvent(2));
                    }

                    @Override
                    public void onError(Throwable e) {
                        super.onError(e);
                        ToastUtil.show(e.getMessage());
                    }
                });
    }

    @Override
    public void addVideo() {


        PictureSelectionSystemModel systemGalleryMode = PictureSelector.create(this)
                .openSystemGallery(SelectMimeType.ofVideo())
                .setSelectionMode(SelectModeConfig.SINGLE)
                .setVideoThumbnailListener(getVideoThumbnailEventListener())
                .setSandboxFileEngine(new MeSandboxFileEngine());
        try {
            systemGalleryMode.forSystemResult(new MeOnResultCallbackListener());
            mLoadingDialog = new PictureLoadingDialog(getContext());
            mLoadingDialog.show();
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    /**
     * 处理视频缩略图
     */
    private OnVideoThumbnailEventListener getVideoThumbnailEventListener() {
        return new MeOnVideoThumbnailEventListener();
    }

    /**
     * 选择结果
     */
    private class MeOnResultCallbackListener implements OnResultCallbackListener<LocalMedia> {
        @Override
        public void onResult(ArrayList<LocalMedia> result) {
            analyticalSelectResults(result);
        }

        @Override
        public void onCancel() {
            try {
                if (mLoadingDialog != null && mLoadingDialog.isShowing()) {
                    mLoadingDialog.dismiss();
                }
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                mLoadingDialog = null;
            }
            Log.i(TAG, "PictureSelector Cancel");
        }
    }


    /**
     * 处理选择结果
     *
     * @param result
     */
    private void analyticalSelectResults(ArrayList<LocalMedia> result) {
        MobclickAgentUtil.clickEvent(GlobalConstant.KEY_LOCAL, GlobalConstant.VALUE_ADDFROMALBUM);

        new Thread(new Runnable() {
            @Override
            public void run() {
                for (int i = 0; i < result.size(); i++) {
                    LocalMedia media = result.get(i);
                    if (media.getWidth() == 0 || media.getHeight() == 0) {
                        if (PictureMimeType.isHasImage(media.getMimeType())) {
                            MediaExtraInfo imageExtraInfo = MediaUtils.getImageSize(getContext(), media.getPath());
                            media.setWidth(imageExtraInfo.getWidth());
                            media.setHeight(imageExtraInfo.getHeight());
                        } else if (PictureMimeType.isHasVideo(media.getMimeType())) {
                            MediaExtraInfo videoExtraInfo = MediaUtils.getVideoSize(getContext(), media.getPath());
                            media.setWidth(videoExtraInfo.getWidth());
                            media.setHeight(videoExtraInfo.getHeight());
                        }
                    }
                    Log.i(TAG, "文件名: " + media.getFileName());
                    Log.i(TAG, "沙盒路径:" + media.getSandboxPath());
                    String fileUrl = media.getSandboxPath();
                    if (!fileUrl.endsWith(".mp4")) {
                        ToastUtil.show(mContext.getText(R.string.please_select_a_video));
                        continue;
                    }
                    if (TextUtils.isEmpty(fileUrl)) {
                        ToastUtil.show(mContext.getText(R.string.add_video_failed));
                        if (mLoadingDialog.isShowing()) {
                            mLoadingDialog.dismiss();
                            mLoadingDialog = null;
                        }
                    }
                    if (!TextUtils.isEmpty(fileUrl) && fileUrl.contains("/")) {
                        String fileName = fileUrl.substring(fileUrl.lastIndexOf("/") + 1);
                        LocalDateTime localDateTime = LocalDateTime.now();
                        String time = localDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                        VideoDbEntity videoDbEntity = new VideoDbEntity(fileName, fileUrl, "0", GlobalConstant.address
                                , time, GlobalConstant.logaddress, "999", GlobalConstant.mobileVision, GlobalConstant.systemVision
                                , GlobalConstant.appVison, UUID.randomUUID().toString(), "0");
                        int finalI = i;
                        VideoDbSingletion.getInstance().insertVideoDb(videoDbEntity, new VideoDbSingletion.Callback() {
                            @Override
                            public void success() {
                                if (finalI == result.size() - 1) {
                                    if (mContext == null) {
                                        return;
                                    }
                                    EventBus.getDefault().post(new LocalFileRefreshEvent(2));

                                    if (mLoadingDialog.isShowing()) {
                                        mLoadingDialog.dismiss();
                                        mLoadingDialog = null;
                                    }
                                }

                            }
                        });
                    }
                }
            }
        }).start();

    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (logigDialog != null) {
            logigDialog.destroy();
            logigDialog = null;
        }
    }

    /**
     * 自定义沙盒文件处理
     */
    private static class MeSandboxFileEngine implements UriToFileTransformEngine {

        @Override
        public void onUriToFileAsyncTransform(Context context, String srcPath, String mineType, OnKeyValueResultCallbackListener call) {
            if (call != null) {
                LocalDateTime localDateTime = LocalDateTime.now();
                String time = localDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

                String name = time.replace(" ", "-");
                name = name.replace(":", "-");

                call.onCallback(srcPath, SandboxTransformUtils.copyPathToSandbox(context, srcPath, mineType, name + ".mp4"));
            }
        }
    }


    /**
     * 处理视频缩略图
     */
    private static class MeOnVideoThumbnailEventListener implements OnVideoThumbnailEventListener {


        public MeOnVideoThumbnailEventListener() {

        }

        @Override
        public void onVideoThumbnail(Context context, String videoPath, OnKeyValueResultCallbackListener call) {
            Glide.with(context).asBitmap().sizeMultiplier(0.6F).load(videoPath).into(new CustomTarget<Bitmap>() {
                @Override
                public void onResourceReady(@NonNull Bitmap resource, @Nullable Transition<? super Bitmap> transition) {
                    ByteArrayOutputStream stream = new ByteArrayOutputStream();
                    resource.compress(Bitmap.CompressFormat.JPEG, 60, stream);
                    FileOutputStream fos = null;
                    String result = null;
                    try {
                        LocalDateTime localDateTime = LocalDateTime.now();
                        LocalDate localDate = localDateTime.toLocalDate();
                        File todayVideoFile = new File(FileUtils.getBlinkPicturePath(App.getContext()), localDate.toString());
                        if (!todayVideoFile.exists()) {
                            todayVideoFile.mkdirs();
                        }
                        LocalTime localTime = localDateTime.toLocalTime();
                        long aLong = localTime.getLong(ChronoField.MILLI_OF_DAY);
                        File targetFile = new File(todayVideoFile.getAbsolutePath(), aLong + ".jpg");
                        fos = new FileOutputStream(targetFile);
                        fos.write(stream.toByteArray());
                        fos.flush();
                        result = targetFile.getAbsolutePath();
                    } catch (IOException e) {
                        e.printStackTrace();
                    } finally {
                        PictureFileUtils.close(fos);
                        PictureFileUtils.close(stream);
                    }
                    if (call != null) {
                        call.onCallback(videoPath, result);
                    }
                }

                @Override
                public void onLoadCleared(@Nullable Drawable placeholder) {
                    if (call != null) {
                        call.onCallback(videoPath, "");
                    }
                }
            });
        }
    }

    public boolean isRefreshing() {
        if (mRefreshLayout != null) {
            return mRefreshLayout.isRefreshing();
        } else {
            return false;
        }
    }

    @Override
    public void shareToSocialMedia() {
        if (checkVideoList.size() != 1) {
            multiSelectShareDialog = new MyDialog(mContext, R.string.confirm, R.string.share_multi_video_warning, MyDialog.CONTENT_CONFIRM);
            multiSelectShareDialog.show();
            return;
        }
        if (!MySetting.getInstance().getUserInfoEntity().isLogin()) {//未登录
            logigDialog.show();
            return;
        }
        MobclickAgentUtil.clickEvent(GlobalConstant.KEY_SHARE,GlobalConstant.VALUE_SHARE_FROM_HOME);
        Intent intent = new Intent(requireActivity(), ShareActivity.class);
        intent.putExtra("videoUrl", checkVideoList.get(0).getUrl());
        intent.putExtra("thumbnail", checkVideoList.get(0).getBase64());
        startActivity(intent);
    }
}
