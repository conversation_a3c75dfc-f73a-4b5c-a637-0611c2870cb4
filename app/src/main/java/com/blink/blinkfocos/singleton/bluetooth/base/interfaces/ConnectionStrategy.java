package com.blink.blinkfocos.singleton.bluetooth.base.interfaces;

import com.blink.blinkfocos.singleton.bluetooth.base.AbstractBluetoothDevice;

public interface ConnectionStrategy {
    void connect(AbstractBluetoothDevice device);
    void disconnect(AbstractBluetoothDevice device);
    void sendData(AbstractBluetoothDevice device,byte[] data);
    void sendData(AbstractBluetoothDevice device,byte[] data,Runnable failedCallback);
    void sendData(AbstractBluetoothDevice device,byte[] data,Runnable successCallback,Runnable failedCallback);
}
