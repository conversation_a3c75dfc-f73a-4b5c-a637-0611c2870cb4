package com.blink.blinkfocos.db;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;

import com.blink.blinkfocos.entity.FolderDbEntity;
import com.blink.blinkfocos.entity.ImageDbEntity;
import com.blink.blinkfocos.entity.VideoDbEntity;

import java.util.List;

@Dao
public interface FolderDao {

    @Query("SELECT * FROM folderdbentity")
    List<FolderDbEntity> getDbFolders();

    @Query("SELECT count(*) FROM folderdbentity")
    int getDbFoldersCount();

    @Insert
    void insertDbFolder(FolderDbEntity folderDb);

    @Delete
    void deleteDbFolder(FolderDbEntity... folderDb);

    //修改(可变参数)
    @Update
    void updateDbFolder(FolderDbEntity... folderDb);

    @Query("SELECT * FROM folderDbEntity WHERE createTime = :createTime")
    List<FolderDbEntity> getFoldersByCreateTime(String createTime);

    @Query("DELETE FROM folderdbentity")
    void deleteAllDbFolders();

}
