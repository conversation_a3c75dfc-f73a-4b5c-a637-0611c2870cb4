apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'

android {
    compileSdk rootProject.compileSdkVersion
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    defaultConfig {
        minSdkVersion 14
        targetSdkVersion rootProject.targetSdkVersion
    }
    buildTypes {
        release {
            consumerProguardFiles 'proguard-rules.pro'
        }
        debug {
            consumerProguardFiles 'proguard-rules.pro'
        }
    }
    compileOptions {
        kotlinOptions.freeCompilerArgs += ['-module-name', "com.github.CymChad.brvah", "-Xjvm-default=all"]
    }
    namespace 'com.chad.library'

}



dependencies {
    implementation 'androidx.annotation:annotation:1.2.0'
    implementation 'androidx.databinding:databinding-runtime:4.2.2'
    implementation 'androidx.recyclerview:recyclerview:1.2.1'
    // 添加 Kotlin 标准库
    implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlin_version"

}
