apply plugin: 'com.android.library'

android {
    compileSdk rootProject.compileSdkVersion
    buildFeatures {
        buildConfig true
    }
    defaultConfig {
        minSdkVersion 26
        targetSdkVersion rootProject.targetSdkVersion

        vectorDrawables.useSupportLibrary = true
    }


    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    namespace 'com.luck.picture.lib'
    lint {
        abortOnError false
    }
}

ext {
    PUBLISH_ARTIFACT_ID = "pictureselector"
}



dependencies {
    implementation 'androidx.appcompat:appcompat:'.concat(supportVersion)
    implementation "androidx.recyclerview:recyclerview:${cfgs.version_recyclerview}"
    implementation "androidx.fragment:fragment:${cfgs.fragment_version}"
    implementation "androidx.activity:activity:${cfgs.activity_version}"
    implementation "androidx.exifinterface:exifinterface:1.3.3"
    implementation "androidx.viewpager2:viewpager2:1.0.0"
    implementation 'androidx.constraintlayout:constraintlayout:2.0.4'
}
