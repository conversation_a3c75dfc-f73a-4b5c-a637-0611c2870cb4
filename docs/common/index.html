<!doctype html>
<html class="no-js">
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1" charset="UTF-8">
    <title>common</title>
<link href="../images/logo-icon.svg" rel="icon" type="image/svg">    <script>var pathToRoot = "../";</script>
    <script>document.documentElement.classList.replace("no-js","js");</script>
    <script>const storage = localStorage.getItem("dokka-dark-mode")
    if (storage == null) {
        const osDarkSchemePreferred = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
        if (osDarkSchemePreferred === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    } else {
        const savedDarkMode = JSON.parse(storage)
        if(savedDarkMode === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    }
    </script>
<script type="text/javascript" src="https://unpkg.com/kotlin-playground@1/dist/playground.min.js" async></script>
<script type="text/javascript" src="../scripts/sourceset_dependencies.js" async></script>
<link href="../styles/style.css" rel="Stylesheet">
<link href="../styles/main.css" rel="Stylesheet">
<link href="../styles/prism.css" rel="Stylesheet">
<link href="../styles/logo-styles.css" rel="Stylesheet">
<link href="../styles/font-jb-sans-auto.css" rel="Stylesheet">
<link href="../ui-kit/ui-kit.min.css" rel="Stylesheet">
<script type="text/javascript" src="../scripts/clipboard.js" async></script>
<script type="text/javascript" src="../scripts/navigation-loader.js" async></script>
<script type="text/javascript" src="../scripts/platform-content-handler.js" async></script>
<script type="text/javascript" src="../scripts/main.js" defer></script>
<script type="text/javascript" src="../scripts/prism.js" async></script>
<script type="text/javascript" src="../ui-kit/ui-kit.min.js" defer></script>
<script type="text/javascript" src="../scripts/symbol-parameters-wrapper_deferred.js" defer></script></head>
<body>
    <div class="root">
    <nav class="navigation theme-dark" id="navigation-wrapper">
<a class="library-name--link" href="../index.html">
                    RootEncoder
            </a>        <button class="navigation-controls--btn navigation-controls--btn_toc ui-kit_mobile-only" id="toc-toggle" type="button">Toggle table of contents
        </button>
        <div class="navigation-controls--break ui-kit_mobile-only"></div>
        <div class="library-version" id="library-version">
        </div>
        <div class="navigation-controls">
        <div class="filter-section filter-section_loading" id="filter-section">
                <button class="platform-tag platform-selector jvm-like" data-active="" data-filter=":common:dokkaHtmlPartial/release">androidJvm</button>
            <div class="dropdown filter-section--dropdown" data-role="dropdown" id="filter-section-dropdown">
                <button class="button button_dropdown filter-section--dropdown-toggle" role="combobox" data-role="dropdown-toggle" aria-controls="platform-tags-listbox" aria-haspopup="listbox" aria-expanded="false" aria-label="Toggle source sets"></button>
                <ul role="listbox" id="platform-tags-listbox" class="dropdown--list" data-role="dropdown-listbox">
                    <div class="dropdown--header"><span>Platform filter</span>
                        <button class="button" data-role="dropdown-toggle" aria-label="Close platform filter">
                            <i class="ui-kit-icon ui-kit-icon_cross"></i>
                        </button>
                    </div>
                        <li role="option" class="dropdown--option platform-selector-option jvm-like" tabindex="0">
                            <label class="checkbox">
                                <input type="checkbox" class="checkbox--input" id=":common:dokkaHtmlPartial/release" data-filter=":common:dokkaHtmlPartial/release">
                                <span class="checkbox--icon"></span>
                                androidJvm
                            </label>
                        </li>
                </ul>
                <div class="dropdown--overlay"></div>
            </div>
        </div>
            <button class="navigation-controls--btn navigation-controls--btn_theme" id="theme-toggle-button" type="button">Switch theme
            </button>
            <div class="navigation-controls--btn navigation-controls--btn_search" id="searchBar" role="button">Search in
                API
            </div>
        </div>
    </nav>
        <div id="container">
            <div class="sidebar" id="leftColumn">
                <div class="dropdown theme-dark_mobile" data-role="dropdown" id="toc-dropdown">
                    <ul role="listbox" id="toc-listbox" class="dropdown--list dropdown--list_toc-list" data-role="dropdown-listbox">
                        <div class="dropdown--header">
                            <span>
                                    RootEncoder
                            </span>
                            <button class="button" data-role="dropdown-toggle" aria-label="Close table of contents">
                                <i class="ui-kit-icon ui-kit-icon_cross"></i>
                            </button>
                        </div>
                        <div class="sidebar--inner" id="sideMenu"></div>
                    </ul>
                    <div class="dropdown--overlay"></div>
                </div>
            </div>
            <div id="main">
<div class="main-content" id="content" pageids="common::////PointingToDeclaration//-719036216">
  <div class="breadcrumbs"></div>
  <div class="cover ">
    <h1 class="cover"><span><span>common</span></span></h1>
  </div>
  <h2 class="">Packages</h2>
  <div class="table"><a data-name="-469286340%2FPackages%2F-719036216" anchor-label="com.pedro.common" id="-469286340%2FPackages%2F-719036216" data-filterable-set=":common:dokkaHtmlPartial/release"></a>
    <div class="table-row" data-filterable-current=":common:dokkaHtmlPartial/release" data-filterable-set=":common:dokkaHtmlPartial/release">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="com.pedro.common/index.html">com.pedro.common</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-469286340%2FPackages%2F-719036216"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">androidJvm</div>
            </div>
          </div>
        </div>
        <div></div>
      </div>
    </div>
<a data-name="1789463402%2FPackages%2F-719036216" anchor-label="com.pedro.common.av1" id="1789463402%2FPackages%2F-719036216" data-filterable-set=":common:dokkaHtmlPartial/release"></a>
    <div class="table-row" data-filterable-current=":common:dokkaHtmlPartial/release" data-filterable-set=":common:dokkaHtmlPartial/release">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="com.pedro.common.av1/index.html">com.pedro.common.av1</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1789463402%2FPackages%2F-719036216"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">androidJvm</div>
            </div>
          </div>
        </div>
        <div></div>
      </div>
    </div>
<a data-name="839182087%2FPackages%2F-719036216" anchor-label="com.pedro.common.base" id="839182087%2FPackages%2F-719036216" data-filterable-set=":common:dokkaHtmlPartial/release"></a>
    <div class="table-row" data-filterable-current=":common:dokkaHtmlPartial/release" data-filterable-set=":common:dokkaHtmlPartial/release">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="com.pedro.common.base/index.html">com.pedro.common.base</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="839182087%2FPackages%2F-719036216"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">androidJvm</div>
            </div>
          </div>
        </div>
        <div></div>
      </div>
    </div>
<a data-name="-1591321605%2FPackages%2F-719036216" anchor-label="com.pedro.common.frame" id="-1591321605%2FPackages%2F-719036216" data-filterable-set=":common:dokkaHtmlPartial/release"></a>
    <div class="table-row" data-filterable-current=":common:dokkaHtmlPartial/release" data-filterable-set=":common:dokkaHtmlPartial/release">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="com.pedro.common.frame/index.html">com.pedro.common.frame</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1591321605%2FPackages%2F-719036216"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">androidJvm</div>
            </div>
          </div>
        </div>
        <div></div>
      </div>
    </div>
<a data-name="-114290374%2FPackages%2F-719036216" anchor-label="com.pedro.common.socket.base" id="-114290374%2FPackages%2F-719036216" data-filterable-set=":common:dokkaHtmlPartial/release"></a>
    <div class="table-row" data-filterable-current=":common:dokkaHtmlPartial/release" data-filterable-set=":common:dokkaHtmlPartial/release">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="com.pedro.common.socket.base/index.html">com.pedro.common.socket.base</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-114290374%2FPackages%2F-719036216"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">androidJvm</div>
            </div>
          </div>
        </div>
        <div></div>
      </div>
    </div>
<a data-name="-339459765%2FPackages%2F-719036216" anchor-label="com.pedro.common.socket.java" id="-339459765%2FPackages%2F-719036216" data-filterable-set=":common:dokkaHtmlPartial/release"></a>
    <div class="table-row" data-filterable-current=":common:dokkaHtmlPartial/release" data-filterable-set=":common:dokkaHtmlPartial/release">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="com.pedro.common.socket.java/index.html">com.pedro.common.socket.java</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-339459765%2FPackages%2F-719036216"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">androidJvm</div>
            </div>
          </div>
        </div>
        <div></div>
      </div>
    </div>
<a data-name="906611637%2FPackages%2F-719036216" anchor-label="com.pedro.common.socket.ktor" id="906611637%2FPackages%2F-719036216" data-filterable-set=":common:dokkaHtmlPartial/release"></a>
    <div class="table-row" data-filterable-current=":common:dokkaHtmlPartial/release" data-filterable-set=":common:dokkaHtmlPartial/release">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="com.pedro.common.socket.ktor/index.html">com.pedro.common.socket.ktor</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="906611637%2FPackages%2F-719036216"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">androidJvm</div>
            </div>
          </div>
        </div>
        <div></div>
      </div>
    </div>
  </div>
</div>
    <div class="footer">
        <a href="#content" id="go-to-top-link" class="footer--button footer--button_go-to-top"></a>
        <span>© 2025 Copyright</span>
        <span class="pull-right">
            <span>Generated by </span>
            <a class="footer--link footer--link_external" href="https://github.com/Kotlin/dokka">
                <span>dokka</span>
            </a>
        </span>
    </div>
            </div>
        </div>
    </div>
</body>
</html>
