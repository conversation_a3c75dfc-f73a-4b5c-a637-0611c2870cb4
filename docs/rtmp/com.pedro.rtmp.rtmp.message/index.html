<!doctype html>
<html class="no-js">
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1" charset="UTF-8">
    <title>com.pedro.rtmp.rtmp.message</title>
<link href="../../images/logo-icon.svg" rel="icon" type="image/svg">    <script>var pathToRoot = "../../";</script>
    <script>document.documentElement.classList.replace("no-js","js");</script>
    <script>const storage = localStorage.getItem("dokka-dark-mode")
    if (storage == null) {
        const osDarkSchemePreferred = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
        if (osDarkSchemePreferred === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    } else {
        const savedDarkMode = JSON.parse(storage)
        if(savedDarkMode === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    }
    </script>
<script type="text/javascript" src="https://unpkg.com/kotlin-playground@1/dist/playground.min.js" async></script>
<script type="text/javascript" src="../../scripts/sourceset_dependencies.js" async></script>
<link href="../../styles/style.css" rel="Stylesheet">
<link href="../../styles/main.css" rel="Stylesheet">
<link href="../../styles/prism.css" rel="Stylesheet">
<link href="../../styles/logo-styles.css" rel="Stylesheet">
<link href="../../styles/font-jb-sans-auto.css" rel="Stylesheet">
<link href="../../ui-kit/ui-kit.min.css" rel="Stylesheet">
<script type="text/javascript" src="../../scripts/clipboard.js" async></script>
<script type="text/javascript" src="../../scripts/navigation-loader.js" async></script>
<script type="text/javascript" src="../../scripts/platform-content-handler.js" async></script>
<script type="text/javascript" src="../../scripts/main.js" defer></script>
<script type="text/javascript" src="../../scripts/prism.js" async></script>
<script type="text/javascript" src="../../ui-kit/ui-kit.min.js" defer></script>
<script type="text/javascript" src="../../scripts/symbol-parameters-wrapper_deferred.js" defer></script></head>
<body>
    <div class="root">
    <nav class="navigation theme-dark" id="navigation-wrapper">
<a class="library-name--link" href="../../index.html">
                    RootEncoder
            </a>        <button class="navigation-controls--btn navigation-controls--btn_toc ui-kit_mobile-only" id="toc-toggle" type="button">Toggle table of contents
        </button>
        <div class="navigation-controls--break ui-kit_mobile-only"></div>
        <div class="library-version" id="library-version">
        </div>
        <div class="navigation-controls">
        <div class="filter-section filter-section_loading" id="filter-section">
                <button class="platform-tag platform-selector jvm-like" data-active="" data-filter=":rtmp:dokkaHtmlPartial/release">androidJvm</button>
            <div class="dropdown filter-section--dropdown" data-role="dropdown" id="filter-section-dropdown">
                <button class="button button_dropdown filter-section--dropdown-toggle" role="combobox" data-role="dropdown-toggle" aria-controls="platform-tags-listbox" aria-haspopup="listbox" aria-expanded="false" aria-label="Toggle source sets"></button>
                <ul role="listbox" id="platform-tags-listbox" class="dropdown--list" data-role="dropdown-listbox">
                    <div class="dropdown--header"><span>Platform filter</span>
                        <button class="button" data-role="dropdown-toggle" aria-label="Close platform filter">
                            <i class="ui-kit-icon ui-kit-icon_cross"></i>
                        </button>
                    </div>
                        <li role="option" class="dropdown--option platform-selector-option jvm-like" tabindex="0">
                            <label class="checkbox">
                                <input type="checkbox" class="checkbox--input" id=":rtmp:dokkaHtmlPartial/release" data-filter=":rtmp:dokkaHtmlPartial/release">
                                <span class="checkbox--icon"></span>
                                androidJvm
                            </label>
                        </li>
                </ul>
                <div class="dropdown--overlay"></div>
            </div>
        </div>
            <button class="navigation-controls--btn navigation-controls--btn_theme" id="theme-toggle-button" type="button">Switch theme
            </button>
            <div class="navigation-controls--btn navigation-controls--btn_search" id="searchBar" role="button">Search in
                API
            </div>
        </div>
    </nav>
        <div id="container">
            <div class="sidebar" id="leftColumn">
                <div class="dropdown theme-dark_mobile" data-role="dropdown" id="toc-dropdown">
                    <ul role="listbox" id="toc-listbox" class="dropdown--list dropdown--list_toc-list" data-role="dropdown-listbox">
                        <div class="dropdown--header">
                            <span>
                                    RootEncoder
                            </span>
                            <button class="button" data-role="dropdown-toggle" aria-label="Close table of contents">
                                <i class="ui-kit-icon ui-kit-icon_cross"></i>
                            </button>
                        </div>
                        <div class="sidebar--inner" id="sideMenu"></div>
                    </ul>
                    <div class="dropdown--overlay"></div>
                </div>
            </div>
            <div id="main">
<div class="main-content" data-page-type="package" id="content" pageids="rtmp::com.pedro.rtmp.rtmp.message////PointingToDeclaration//-958441266">
  <div class="breadcrumbs"><a href="../index.html">rtmp</a><span class="delimiter">/</span><span class="current">com.pedro.rtmp.rtmp.message</span></div>
  <div class="cover ">
    <h1 class="cover"><span><span>Package-level</span></span> <span><span>declarations</span></span></h1>
  </div>
  <div class="tabbedcontent">
    <div class="tabs-section" tabs-section="tabs-section"><button class="section-tab" data-active="" data-togglable="TYPE">Types</button></div>
    <div class="tabs-section-body">
      <div data-togglable="TYPE">
        <h2 class="">Types</h2>
        <div class="table"><a data-name="-1471898888%2FClasslikes%2F-958441266" anchor-label="Abort" id="-1471898888%2FClasslikes%2F-958441266" data-filterable-set=":rtmp:dokkaHtmlPartial/release"></a>
          <div class="table-row" data-filterable-current=":rtmp:dokkaHtmlPartial/release" data-filterable-set=":rtmp:dokkaHtmlPartial/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-abort/index.html"><span><span>Abort</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1471898888%2FClasslikes%2F-958441266"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":rtmp:dokkaHtmlPartial/release"><div class="symbol monospace"><span class="token keyword">class </span><a href="-abort/index.html">Abort</a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">chunkStreamId<span class="token operator">: </span><a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin/-int/index.html">Int</a><span class="token operator"> = </span><span class="token constant">0</span></span></span><span class="token punctuation">)</span> : <a href="-rtmp-message/index.html">RtmpMessage</a></div><div class="brief "><p class="paragraph">Created by pedro on 21/04/21.</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1553138350%2FClasslikes%2F-958441266" anchor-label="Acknowledgement" id="1553138350%2FClasslikes%2F-958441266" data-filterable-set=":rtmp:dokkaHtmlPartial/release"></a>
          <div class="table-row" data-filterable-current=":rtmp:dokkaHtmlPartial/release" data-filterable-set=":rtmp:dokkaHtmlPartial/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-acknowledgement/index.html"><span><span>Acknowledgement</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1553138350%2FClasslikes%2F-958441266"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":rtmp:dokkaHtmlPartial/release"><div class="symbol monospace"><span class="token keyword">class </span><a href="-acknowledgement/index.html">Acknowledgement</a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">sequenceNumber<span class="token operator">: </span><a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin/-int/index.html">Int</a><span class="token operator"> = </span><span class="token constant">0</span></span></span><span class="token punctuation">)</span> : <a href="-rtmp-message/index.html">RtmpMessage</a></div><div class="brief "><p class="paragraph">Created by pedro on 21/04/21.</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1876925385%2FClasslikes%2F-958441266" anchor-label="Aggregate" id="1876925385%2FClasslikes%2F-958441266" data-filterable-set=":rtmp:dokkaHtmlPartial/release"></a>
          <div class="table-row" data-filterable-current=":rtmp:dokkaHtmlPartial/release" data-filterable-set=":rtmp:dokkaHtmlPartial/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-aggregate/index.html"><span><span>Aggregate</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1876925385%2FClasslikes%2F-958441266"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":rtmp:dokkaHtmlPartial/release"><div class="symbol monospace"><span class="token keyword">class </span><a href="-aggregate/index.html">Aggregate</a> : <a href="-rtmp-message/index.html">RtmpMessage</a></div><div class="brief "><p class="paragraph">Created by pedro on 21/04/21.</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1394542866%2FClasslikes%2F-958441266" anchor-label="Audio" id="1394542866%2FClasslikes%2F-958441266" data-filterable-set=":rtmp:dokkaHtmlPartial/release"></a>
          <div class="table-row" data-filterable-current=":rtmp:dokkaHtmlPartial/release" data-filterable-set=":rtmp:dokkaHtmlPartial/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-audio/index.html"><span><span>Audio</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1394542866%2FClasslikes%2F-958441266"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":rtmp:dokkaHtmlPartial/release"><div class="symbol monospace"><span class="token keyword">class </span><a href="-audio/index.html">Audio</a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">flvPacket<span class="token operator">: </span><a href="../com.pedro.rtmp.flv/-flv-packet/index.html">FlvPacket</a><span class="token operator"> = </span>FlvPacket()<span class="token punctuation">, </span></span><span class="parameter ">streamId<span class="token operator">: </span><a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin/-int/index.html">Int</a><span class="token operator"> = </span><span class="token constant">0</span></span></span><span class="token punctuation">)</span> : <a href="-rtmp-message/index.html">RtmpMessage</a></div><div class="brief "><p class="paragraph">Created by pedro on 21/04/21.</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="467433197%2FClasslikes%2F-958441266" anchor-label="BasicHeader" id="467433197%2FClasslikes%2F-958441266" data-filterable-set=":rtmp:dokkaHtmlPartial/release"></a>
          <div class="table-row" data-filterable-current=":rtmp:dokkaHtmlPartial/release" data-filterable-set=":rtmp:dokkaHtmlPartial/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-basic-header/index.html"><span>Basic</span><wbr><span><span>Header</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="467433197%2FClasslikes%2F-958441266"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":rtmp:dokkaHtmlPartial/release"><div class="symbol monospace"><span class="token keyword">class </span><a href="-basic-header/index.html">BasicHeader</a><span class="token punctuation">(</span><span class="parameters "><span class="parameter "><span class="token keyword">val </span>chunkType<span class="token operator">: </span><a href="../com.pedro.rtmp.rtmp.chunk/-chunk-type/index.html">ChunkType</a><span class="token punctuation">, </span></span><span class="parameter "><span class="token keyword">val </span>chunkStreamId<span class="token operator">: </span><a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin/-int/index.html">Int</a></span></span><span class="token punctuation">)</span></div><div class="brief "><p class="paragraph">Created by pedro on 21/04/21.</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="515734055%2FClasslikes%2F-958441266" anchor-label="MessageType" id="515734055%2FClasslikes%2F-958441266" data-filterable-set=":rtmp:dokkaHtmlPartial/release"></a>
          <div class="table-row" data-filterable-current=":rtmp:dokkaHtmlPartial/release" data-filterable-set=":rtmp:dokkaHtmlPartial/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-message-type/index.html"><span>Message</span><wbr><span><span>Type</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="515734055%2FClasslikes%2F-958441266"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":rtmp:dokkaHtmlPartial/release"><div class="symbol monospace"><span class="token keyword">enum </span><a href="-message-type/index.html">MessageType</a> : <a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin/-enum/index.html">Enum</a><span class="token operator">&lt;</span><a href="-message-type/index.html">MessageType</a><span class="token operator">&gt; </span></div><div class="brief "><p class="paragraph">Created by pedro on 21/04/21.</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-364125048%2FClasslikes%2F-958441266" anchor-label="RtmpHeader" id="-364125048%2FClasslikes%2F-958441266" data-filterable-set=":rtmp:dokkaHtmlPartial/release"></a>
          <div class="table-row" data-filterable-current=":rtmp:dokkaHtmlPartial/release" data-filterable-set=":rtmp:dokkaHtmlPartial/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-rtmp-header/index.html"><span>Rtmp</span><wbr><span><span>Header</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-364125048%2FClasslikes%2F-958441266"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":rtmp:dokkaHtmlPartial/release"><div class="symbol monospace"><span class="token keyword">class </span><a href="-rtmp-header/index.html">RtmpHeader</a><span class="token punctuation">(</span><span class="parameters "><span class="parameter "><span class="token keyword">var </span>basicHeader<span class="token operator">: </span><a href="-basic-header/index.html">BasicHeader</a></span></span><span class="token punctuation">)</span></div><div class="brief "><p class="paragraph">Created by pedro on 20/04/21.</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="662453318%2FClasslikes%2F-958441266" anchor-label="RtmpMessage" id="662453318%2FClasslikes%2F-958441266" data-filterable-set=":rtmp:dokkaHtmlPartial/release"></a>
          <div class="table-row" data-filterable-current=":rtmp:dokkaHtmlPartial/release" data-filterable-set=":rtmp:dokkaHtmlPartial/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-rtmp-message/index.html"><span>Rtmp</span><wbr><span><span>Message</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="662453318%2FClasslikes%2F-958441266"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":rtmp:dokkaHtmlPartial/release"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword">class </span><a href="-rtmp-message/index.html">RtmpMessage</a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">basicHeader<span class="token operator">: </span><a href="-basic-header/index.html">BasicHeader</a></span></span><span class="token punctuation">)</span></div><div class="brief "><p class="paragraph">Created by pedro on 20/04/21.</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1403272178%2FClasslikes%2F-958441266" anchor-label="SetChunkSize" id="-1403272178%2FClasslikes%2F-958441266" data-filterable-set=":rtmp:dokkaHtmlPartial/release"></a>
          <div class="table-row" data-filterable-current=":rtmp:dokkaHtmlPartial/release" data-filterable-set=":rtmp:dokkaHtmlPartial/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-set-chunk-size/index.html"><span>Set</span><wbr><span>Chunk</span><wbr><span><span>Size</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1403272178%2FClasslikes%2F-958441266"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":rtmp:dokkaHtmlPartial/release"><div class="symbol monospace"><span class="token keyword">class </span><a href="-set-chunk-size/index.html">SetChunkSize</a><span class="token punctuation">(</span><span class="parameters "><span class="parameter "><span class="token keyword">var </span>chunkSize<span class="token operator">: </span><a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin/-int/index.html">Int</a><span class="token operator"> = </span>RtmpConfig.DEFAULT_CHUNK_SIZE</span></span><span class="token punctuation">)</span> : <a href="-rtmp-message/index.html">RtmpMessage</a></div><div class="brief "><p class="paragraph">Created by pedro on 21/04/21.</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-487821235%2FClasslikes%2F-958441266" anchor-label="SetPeerBandwidth" id="-487821235%2FClasslikes%2F-958441266" data-filterable-set=":rtmp:dokkaHtmlPartial/release"></a>
          <div class="table-row" data-filterable-current=":rtmp:dokkaHtmlPartial/release" data-filterable-set=":rtmp:dokkaHtmlPartial/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-set-peer-bandwidth/index.html"><span>Set</span><wbr><span>Peer</span><wbr><span><span>Bandwidth</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-487821235%2FClasslikes%2F-958441266"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":rtmp:dokkaHtmlPartial/release"><div class="symbol monospace"><span class="token keyword">class </span><a href="-set-peer-bandwidth/index.html">SetPeerBandwidth</a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">acknowledgementWindowSize<span class="token operator">: </span><a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin/-int/index.html">Int</a><span class="token operator"> = </span><span class="token constant">0</span><span class="token punctuation">, </span></span><span class="parameter ">type<span class="token operator">: </span><a href="-set-peer-bandwidth/-type/index.html">SetPeerBandwidth.Type</a><span class="token operator"> = </span>Type.DYNAMIC</span></span><span class="token punctuation">)</span> : <a href="-rtmp-message/index.html">RtmpMessage</a></div><div class="brief "><p class="paragraph">Created by pedro on 21/04/21.</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1941560307%2FClasslikes%2F-958441266" anchor-label="Video" id="-1941560307%2FClasslikes%2F-958441266" data-filterable-set=":rtmp:dokkaHtmlPartial/release"></a>
          <div class="table-row" data-filterable-current=":rtmp:dokkaHtmlPartial/release" data-filterable-set=":rtmp:dokkaHtmlPartial/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-video/index.html"><span><span>Video</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1941560307%2FClasslikes%2F-958441266"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":rtmp:dokkaHtmlPartial/release"><div class="symbol monospace"><span class="token keyword">class </span><a href="-video/index.html">Video</a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">flvPacket<span class="token operator">: </span><a href="../com.pedro.rtmp.flv/-flv-packet/index.html">FlvPacket</a><span class="token operator"> = </span>FlvPacket()<span class="token punctuation">, </span></span><span class="parameter ">streamId<span class="token operator">: </span><a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin/-int/index.html">Int</a><span class="token operator"> = </span><span class="token constant">0</span></span></span><span class="token punctuation">)</span> : <a href="-rtmp-message/index.html">RtmpMessage</a></div><div class="brief "><p class="paragraph">Created by pedro on 21/04/21.</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="657621501%2FClasslikes%2F-958441266" anchor-label="WindowAcknowledgementSize" id="657621501%2FClasslikes%2F-958441266" data-filterable-set=":rtmp:dokkaHtmlPartial/release"></a>
          <div class="table-row" data-filterable-current=":rtmp:dokkaHtmlPartial/release" data-filterable-set=":rtmp:dokkaHtmlPartial/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-window-acknowledgement-size/index.html"><span>Window</span><wbr><span>Acknowledgement</span><wbr><span><span>Size</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="657621501%2FClasslikes%2F-958441266"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":rtmp:dokkaHtmlPartial/release"><div class="symbol monospace"><span class="token keyword">class </span><a href="-window-acknowledgement-size/index.html">WindowAcknowledgementSize</a><span class="token punctuation">(</span><span class="parameters "><span class="parameter "><span class="token keyword">var </span>acknowledgementWindowSize<span class="token operator">: </span><a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin/-int/index.html">Int</a><span class="token operator"> = </span><span class="token constant">0</span><span class="token punctuation">, </span></span><span class="parameter ">timeStamp<span class="token operator">: </span><a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin/-int/index.html">Int</a><span class="token operator"> = </span><span class="token constant">0</span></span></span><span class="token punctuation">)</span> : <a href="-rtmp-message/index.html">RtmpMessage</a></div><div class="brief "><p class="paragraph">Created by pedro on 21/04/21.</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
    <div class="footer">
        <a href="#content" id="go-to-top-link" class="footer--button footer--button_go-to-top"></a>
        <span>© 2025 Copyright</span>
        <span class="pull-right">
            <span>Generated by </span>
            <a class="footer--link footer--link_external" href="https://github.com/Kotlin/dokka">
                <span>dokka</span>
            </a>
        </span>
    </div>
            </div>
        </div>
    </div>
</body>
</html>
