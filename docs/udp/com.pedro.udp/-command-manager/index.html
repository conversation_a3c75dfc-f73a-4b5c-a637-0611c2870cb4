<!doctype html>
<html class="no-js">
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1" charset="UTF-8">
    <title>CommandManager</title>
<link href="../../../images/logo-icon.svg" rel="icon" type="image/svg">    <script>var pathToRoot = "../../../";</script>
    <script>document.documentElement.classList.replace("no-js","js");</script>
    <script>const storage = localStorage.getItem("dokka-dark-mode")
    if (storage == null) {
        const osDarkSchemePreferred = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
        if (osDarkSchemePreferred === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    } else {
        const savedDarkMode = JSON.parse(storage)
        if(savedDarkMode === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    }
    </script>
<script type="text/javascript" src="https://unpkg.com/kotlin-playground@1/dist/playground.min.js" async></script>
<script type="text/javascript" src="../../../scripts/sourceset_dependencies.js" async></script>
<link href="../../../styles/style.css" rel="Stylesheet">
<link href="../../../styles/main.css" rel="Stylesheet">
<link href="../../../styles/prism.css" rel="Stylesheet">
<link href="../../../styles/logo-styles.css" rel="Stylesheet">
<link href="../../../styles/font-jb-sans-auto.css" rel="Stylesheet">
<link href="../../../ui-kit/ui-kit.min.css" rel="Stylesheet">
<script type="text/javascript" src="../../../scripts/clipboard.js" async></script>
<script type="text/javascript" src="../../../scripts/navigation-loader.js" async></script>
<script type="text/javascript" src="../../../scripts/platform-content-handler.js" async></script>
<script type="text/javascript" src="../../../scripts/main.js" defer></script>
<script type="text/javascript" src="../../../scripts/prism.js" async></script>
<script type="text/javascript" src="../../../ui-kit/ui-kit.min.js" defer></script>
<script type="text/javascript" src="../../../scripts/symbol-parameters-wrapper_deferred.js" defer></script></head>
<body>
    <div class="root">
    <nav class="navigation theme-dark" id="navigation-wrapper">
<a class="library-name--link" href="../../../index.html">
                    RootEncoder
            </a>        <button class="navigation-controls--btn navigation-controls--btn_toc ui-kit_mobile-only" id="toc-toggle" type="button">Toggle table of contents
        </button>
        <div class="navigation-controls--break ui-kit_mobile-only"></div>
        <div class="library-version" id="library-version">
        </div>
        <div class="navigation-controls">
        <div class="filter-section filter-section_loading" id="filter-section">
                <button class="platform-tag platform-selector jvm-like" data-active="" data-filter=":udp:dokkaHtmlPartial/release">androidJvm</button>
            <div class="dropdown filter-section--dropdown" data-role="dropdown" id="filter-section-dropdown">
                <button class="button button_dropdown filter-section--dropdown-toggle" role="combobox" data-role="dropdown-toggle" aria-controls="platform-tags-listbox" aria-haspopup="listbox" aria-expanded="false" aria-label="Toggle source sets"></button>
                <ul role="listbox" id="platform-tags-listbox" class="dropdown--list" data-role="dropdown-listbox">
                    <div class="dropdown--header"><span>Platform filter</span>
                        <button class="button" data-role="dropdown-toggle" aria-label="Close platform filter">
                            <i class="ui-kit-icon ui-kit-icon_cross"></i>
                        </button>
                    </div>
                        <li role="option" class="dropdown--option platform-selector-option jvm-like" tabindex="0">
                            <label class="checkbox">
                                <input type="checkbox" class="checkbox--input" id=":udp:dokkaHtmlPartial/release" data-filter=":udp:dokkaHtmlPartial/release">
                                <span class="checkbox--icon"></span>
                                androidJvm
                            </label>
                        </li>
                </ul>
                <div class="dropdown--overlay"></div>
            </div>
        </div>
            <button class="navigation-controls--btn navigation-controls--btn_theme" id="theme-toggle-button" type="button">Switch theme
            </button>
            <div class="navigation-controls--btn navigation-controls--btn_search" id="searchBar" role="button">Search in
                API
            </div>
        </div>
    </nav>
        <div id="container">
            <div class="sidebar" id="leftColumn">
                <div class="dropdown theme-dark_mobile" data-role="dropdown" id="toc-dropdown">
                    <ul role="listbox" id="toc-listbox" class="dropdown--list dropdown--list_toc-list" data-role="dropdown-listbox">
                        <div class="dropdown--header">
                            <span>
                                    RootEncoder
                            </span>
                            <button class="button" data-role="dropdown-toggle" aria-label="Close table of contents">
                                <i class="ui-kit-icon ui-kit-icon_cross"></i>
                            </button>
                        </div>
                        <div class="sidebar--inner" id="sideMenu"></div>
                    </ul>
                    <div class="dropdown--overlay"></div>
                </div>
            </div>
            <div id="main">
<div class="main-content" data-page-type="classlike" id="content" pageids="udp::com.pedro.udp/CommandManager///PointingToDeclaration//1029615748">
  <div class="breadcrumbs"><a href="../../index.html">udp</a><span class="delimiter">/</span><a href="../index.html">com.pedro.udp</a><span class="delimiter">/</span><span class="current">CommandManager</span></div>
  <div class="cover ">
    <h1 class="cover"><span>Command</span><wbr><span><span>Manager</span></span></h1>
    <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":udp:dokkaHtmlPartial/release"><div class="symbol monospace"><span class="token keyword">class </span><a href="index.html">CommandManager</a></div><p class="paragraph">Created by pedro on 6/3/24.</p></div></div>
  </div>
  <div class="tabbedcontent">
    <div class="tabs-section" tabs-section="tabs-section"><button class="section-tab" data-active="" data-togglable="CONSTRUCTOR,TYPE,PROPERTY,FUNCTION">Members</button></div>
    <div class="tabs-section-body">
      <div data-togglable="CONSTRUCTOR">
        <h2 class="">Constructors</h2>
        <div class="table"><a data-name="-1401435797%2FConstructors%2F1029615748" anchor-label="CommandManager" id="-1401435797%2FConstructors%2F1029615748" data-filterable-set=":udp:dokkaHtmlPartial/release"></a>
          <div class="table-row" data-togglable="CONSTRUCTOR" data-filterable-current=":udp:dokkaHtmlPartial/release" data-filterable-set=":udp:dokkaHtmlPartial/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-command-manager.html"><span>Command</span><wbr><span><span>Manager</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1401435797%2FConstructors%2F1029615748"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":udp:dokkaHtmlPartial/release"><div class="symbol monospace"><span class="token keyword">constructor</span><span class="token punctuation">(</span><span class="token punctuation">)</span></div></div></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div data-togglable="PROPERTY">
        <h2 class="">Properties</h2>
        <div class="table"><a data-name="-1871030451%2FProperties%2F1029615748" anchor-label="audioCodec" id="-1871030451%2FProperties%2F1029615748" data-filterable-set=":udp:dokkaHtmlPartial/release"></a>
          <div class="table-row" data-filterable-current=":udp:dokkaHtmlPartial/release" data-filterable-set=":udp:dokkaHtmlPartial/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="audio-codec.html"><span>audio</span><wbr><span><span>Codec</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1871030451%2FProperties%2F1029615748"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":udp:dokkaHtmlPartial/release"><div class="symbol monospace"><span class="token keyword">var </span><a href="audio-codec.html">audioCodec</a><span class="token operator">: </span><a href="../../../common/com.pedro.common/-audio-codec/index.html">AudioCodec</a></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1308877797%2FProperties%2F1029615748" anchor-label="audioDisabled" id="-1308877797%2FProperties%2F1029615748" data-filterable-set=":udp:dokkaHtmlPartial/release"></a>
          <div class="table-row" data-filterable-current=":udp:dokkaHtmlPartial/release" data-filterable-set=":udp:dokkaHtmlPartial/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="audio-disabled.html"><span>audio</span><wbr><span><span>Disabled</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1308877797%2FProperties%2F1029615748"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":udp:dokkaHtmlPartial/release"><div class="symbol monospace"><span class="token keyword">var </span><a href="audio-disabled.html">audioDisabled</a><span class="token operator">: </span><a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin/-boolean/index.html">Boolean</a></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-2105212795%2FProperties%2F1029615748" anchor-label="host" id="-2105212795%2FProperties%2F1029615748" data-filterable-set=":udp:dokkaHtmlPartial/release"></a>
          <div class="table-row" data-filterable-current=":udp:dokkaHtmlPartial/release" data-filterable-set=":udp:dokkaHtmlPartial/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="host.html"><span><span>host</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-2105212795%2FProperties%2F1029615748"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":udp:dokkaHtmlPartial/release"><div class="symbol monospace"><span class="token keyword">var </span><a href="host.html">host</a><span class="token operator">: </span><a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin/-string/index.html">String</a></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-600547201%2FProperties%2F1029615748" anchor-label="MTU" id="-600547201%2FProperties%2F1029615748" data-filterable-set=":udp:dokkaHtmlPartial/release"></a>
          <div class="table-row" data-filterable-current=":udp:dokkaHtmlPartial/release" data-filterable-set=":udp:dokkaHtmlPartial/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-m-t-u.html"><span><span>MTU</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-600547201%2FProperties%2F1029615748"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":udp:dokkaHtmlPartial/release"><div class="symbol monospace"><span class="token keyword">var </span><a href="-m-t-u.html">MTU</a><span class="token operator">: </span><a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin/-int/index.html">Int</a></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="33808690%2FProperties%2F1029615748" anchor-label="videoCodec" id="33808690%2FProperties%2F1029615748" data-filterable-set=":udp:dokkaHtmlPartial/release"></a>
          <div class="table-row" data-filterable-current=":udp:dokkaHtmlPartial/release" data-filterable-set=":udp:dokkaHtmlPartial/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="video-codec.html"><span>video</span><wbr><span><span>Codec</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="33808690%2FProperties%2F1029615748"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":udp:dokkaHtmlPartial/release"><div class="symbol monospace"><span class="token keyword">var </span><a href="video-codec.html">videoCodec</a><span class="token operator">: </span><a href="../../../common/com.pedro.common/-video-codec/index.html">VideoCodec</a></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="646056982%2FProperties%2F1029615748" anchor-label="videoDisabled" id="646056982%2FProperties%2F1029615748" data-filterable-set=":udp:dokkaHtmlPartial/release"></a>
          <div class="table-row" data-filterable-current=":udp:dokkaHtmlPartial/release" data-filterable-set=":udp:dokkaHtmlPartial/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="video-disabled.html"><span>video</span><wbr><span><span>Disabled</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="646056982%2FProperties%2F1029615748"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":udp:dokkaHtmlPartial/release"><div class="symbol monospace"><span class="token keyword">var </span><a href="video-disabled.html">videoDisabled</a><span class="token operator">: </span><a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin/-boolean/index.html">Boolean</a></div></div></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div data-togglable="FUNCTION">
        <h2 class="">Functions</h2>
        <div class="table"><a data-name="992957918%2FFunctions%2F1029615748" anchor-label="reset" id="992957918%2FFunctions%2F1029615748" data-filterable-set=":udp:dokkaHtmlPartial/release"></a>
          <div class="table-row" data-filterable-current=":udp:dokkaHtmlPartial/release" data-filterable-set=":udp:dokkaHtmlPartial/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="reset.html"><span><span>reset</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="992957918%2FFunctions%2F1029615748"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":udp:dokkaHtmlPartial/release"><div class="symbol monospace"><span class="token keyword">fun </span><a href="reset.html"><span class="token function">reset</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1029844883%2FFunctions%2F1029615748" anchor-label="writeData" id="-1029844883%2FFunctions%2F1029615748" data-filterable-set=":udp:dokkaHtmlPartial/release"></a>
          <div class="table-row" data-filterable-current=":udp:dokkaHtmlPartial/release" data-filterable-set=":udp:dokkaHtmlPartial/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="write-data.html"><span>write</span><wbr><span><span>Data</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1029844883%2FFunctions%2F1029615748"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":udp:dokkaHtmlPartial/release"><div class="symbol monospace"><span class="token keyword">suspend </span><span class="token keyword">fun </span><a href="write-data.html"><span class="token function">writeData</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">packet<span class="token operator">: </span><a href="../../../srt/com.pedro.srt.mpeg2ts/-mpeg-ts-packet/index.html">MpegTsPacket</a><span class="token punctuation">, </span></span><span class="parameter ">socket<span class="token operator">: </span><a href="../../com.pedro.udp.utils/-udp-socket/index.html">UdpSocket</a><span class="token operator">?</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin/-int/index.html">Int</a></div></div></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
    <div class="footer">
        <a href="#content" id="go-to-top-link" class="footer--button footer--button_go-to-top"></a>
        <span>© 2025 Copyright</span>
        <span class="pull-right">
            <span>Generated by </span>
            <a class="footer--link footer--link_external" href="https://github.com/Kotlin/dokka">
                <span>dokka</span>
            </a>
        </span>
    </div>
            </div>
        </div>
    </div>
</body>
</html>
