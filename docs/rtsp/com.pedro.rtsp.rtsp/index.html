<!doctype html>
<html class="no-js">
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1" charset="UTF-8">
    <title>com.pedro.rtsp.rtsp</title>
<link href="../../images/logo-icon.svg" rel="icon" type="image/svg">    <script>var pathToRoot = "../../";</script>
    <script>document.documentElement.classList.replace("no-js","js");</script>
    <script>const storage = localStorage.getItem("dokka-dark-mode")
    if (storage == null) {
        const osDarkSchemePreferred = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
        if (osDarkSchemePreferred === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    } else {
        const savedDarkMode = JSON.parse(storage)
        if(savedDarkMode === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    }
    </script>
<script type="text/javascript" src="https://unpkg.com/kotlin-playground@1/dist/playground.min.js" async></script>
<script type="text/javascript" src="../../scripts/sourceset_dependencies.js" async></script>
<link href="../../styles/style.css" rel="Stylesheet">
<link href="../../styles/main.css" rel="Stylesheet">
<link href="../../styles/prism.css" rel="Stylesheet">
<link href="../../styles/logo-styles.css" rel="Stylesheet">
<link href="../../styles/font-jb-sans-auto.css" rel="Stylesheet">
<link href="../../ui-kit/ui-kit.min.css" rel="Stylesheet">
<script type="text/javascript" src="../../scripts/clipboard.js" async></script>
<script type="text/javascript" src="../../scripts/navigation-loader.js" async></script>
<script type="text/javascript" src="../../scripts/platform-content-handler.js" async></script>
<script type="text/javascript" src="../../scripts/main.js" defer></script>
<script type="text/javascript" src="../../scripts/prism.js" async></script>
<script type="text/javascript" src="../../ui-kit/ui-kit.min.js" defer></script>
<script type="text/javascript" src="../../scripts/symbol-parameters-wrapper_deferred.js" defer></script></head>
<body>
    <div class="root">
    <nav class="navigation theme-dark" id="navigation-wrapper">
<a class="library-name--link" href="../../index.html">
                    RootEncoder
            </a>        <button class="navigation-controls--btn navigation-controls--btn_toc ui-kit_mobile-only" id="toc-toggle" type="button">Toggle table of contents
        </button>
        <div class="navigation-controls--break ui-kit_mobile-only"></div>
        <div class="library-version" id="library-version">
        </div>
        <div class="navigation-controls">
        <div class="filter-section filter-section_loading" id="filter-section">
                <button class="platform-tag platform-selector jvm-like" data-active="" data-filter=":rtsp:dokkaHtmlPartial/release">androidJvm</button>
            <div class="dropdown filter-section--dropdown" data-role="dropdown" id="filter-section-dropdown">
                <button class="button button_dropdown filter-section--dropdown-toggle" role="combobox" data-role="dropdown-toggle" aria-controls="platform-tags-listbox" aria-haspopup="listbox" aria-expanded="false" aria-label="Toggle source sets"></button>
                <ul role="listbox" id="platform-tags-listbox" class="dropdown--list" data-role="dropdown-listbox">
                    <div class="dropdown--header"><span>Platform filter</span>
                        <button class="button" data-role="dropdown-toggle" aria-label="Close platform filter">
                            <i class="ui-kit-icon ui-kit-icon_cross"></i>
                        </button>
                    </div>
                        <li role="option" class="dropdown--option platform-selector-option jvm-like" tabindex="0">
                            <label class="checkbox">
                                <input type="checkbox" class="checkbox--input" id=":rtsp:dokkaHtmlPartial/release" data-filter=":rtsp:dokkaHtmlPartial/release">
                                <span class="checkbox--icon"></span>
                                androidJvm
                            </label>
                        </li>
                </ul>
                <div class="dropdown--overlay"></div>
            </div>
        </div>
            <button class="navigation-controls--btn navigation-controls--btn_theme" id="theme-toggle-button" type="button">Switch theme
            </button>
            <div class="navigation-controls--btn navigation-controls--btn_search" id="searchBar" role="button">Search in
                API
            </div>
        </div>
    </nav>
        <div id="container">
            <div class="sidebar" id="leftColumn">
                <div class="dropdown theme-dark_mobile" data-role="dropdown" id="toc-dropdown">
                    <ul role="listbox" id="toc-listbox" class="dropdown--list dropdown--list_toc-list" data-role="dropdown-listbox">
                        <div class="dropdown--header">
                            <span>
                                    RootEncoder
                            </span>
                            <button class="button" data-role="dropdown-toggle" aria-label="Close table of contents">
                                <i class="ui-kit-icon ui-kit-icon_cross"></i>
                            </button>
                        </div>
                        <div class="sidebar--inner" id="sideMenu"></div>
                    </ul>
                    <div class="dropdown--overlay"></div>
                </div>
            </div>
            <div id="main">
<div class="main-content" data-page-type="package" id="content" pageids="rtsp::com.pedro.rtsp.rtsp////PointingToDeclaration//631315412">
  <div class="breadcrumbs"><a href="../index.html">rtsp</a><span class="delimiter">/</span><span class="current">com.pedro.rtsp.rtsp</span></div>
  <div class="cover ">
    <h1 class="cover"><span><span>Package-level</span></span> <span><span>declarations</span></span></h1>
  </div>
  <div class="tabbedcontent">
    <div class="tabs-section" tabs-section="tabs-section"><button class="section-tab" data-active="" data-togglable="TYPE">Types</button></div>
    <div class="tabs-section-body">
      <div data-togglable="TYPE">
        <h2 class="">Types</h2>
        <div class="table"><a data-name="-1861725527%2FClasslikes%2F631315412" anchor-label="Protocol" id="-1861725527%2FClasslikes%2F631315412" data-filterable-set=":rtsp:dokkaHtmlPartial/release"></a>
          <div class="table-row" data-filterable-current=":rtsp:dokkaHtmlPartial/release" data-filterable-set=":rtsp:dokkaHtmlPartial/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-protocol/index.html"><span><span>Protocol</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1861725527%2FClasslikes%2F631315412"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":rtsp:dokkaHtmlPartial/release"><div class="symbol monospace"><span class="token keyword">enum </span><a href="-protocol/index.html">Protocol</a> : <a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin/-enum/index.html">Enum</a><span class="token operator">&lt;</span><a href="-protocol/index.html">Protocol</a><span class="token operator">&gt; </span></div><div class="brief "><p class="paragraph">Created by pedro on 24/02/17.</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1380682878%2FClasslikes%2F631315412" anchor-label="RtpFrame" id="-1380682878%2FClasslikes%2F631315412" data-filterable-set=":rtsp:dokkaHtmlPartial/release"></a>
          <div class="table-row" data-filterable-current=":rtsp:dokkaHtmlPartial/release" data-filterable-set=":rtsp:dokkaHtmlPartial/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-rtp-frame/index.html"><span>Rtp</span><wbr><span><span>Frame</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1380682878%2FClasslikes%2F631315412"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":rtsp:dokkaHtmlPartial/release"><div class="symbol monospace"><span class="token keyword">data </span><span class="token keyword">class </span><a href="-rtp-frame/index.html">RtpFrame</a><span class="token punctuation">(</span><span class="parameters "><span class="parameter "><span class="token keyword">val </span>buffer<span class="token operator">: </span><a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin/-byte-array/index.html">ByteArray</a><span class="token punctuation">, </span></span><span class="parameter "><span class="token keyword">val </span>timeStamp<span class="token operator">: </span><a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin/-long/index.html">Long</a><span class="token punctuation">, </span></span><span class="parameter "><span class="token keyword">val </span>length<span class="token operator">: </span><a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin/-int/index.html">Int</a><span class="token punctuation">, </span></span><span class="parameter "><span class="token keyword">val </span>channelIdentifier<span class="token operator">: </span><a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin/-int/index.html">Int</a></span></span><span class="token punctuation">)</span></div><div class="brief "><p class="paragraph">Created by pedro on 7/11/18.</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1210976073%2FClasslikes%2F631315412" anchor-label="RtspClient" id="-1210976073%2FClasslikes%2F631315412" data-filterable-set=":rtsp:dokkaHtmlPartial/release"></a>
          <div class="table-row" data-filterable-current=":rtsp:dokkaHtmlPartial/release" data-filterable-set=":rtsp:dokkaHtmlPartial/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-rtsp-client/index.html"><span>Rtsp</span><wbr><span><span>Client</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1210976073%2FClasslikes%2F631315412"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":rtsp:dokkaHtmlPartial/release"><div class="symbol monospace"><span class="token keyword">class </span><a href="-rtsp-client/index.html">RtspClient</a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">connectChecker<span class="token operator">: </span><a href="../../common/com.pedro.common/-connect-checker/index.html">ConnectChecker</a></span></span><span class="token punctuation">)</span></div><div class="brief "><p class="paragraph">Created by pedro on 10/02/17.</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-673710707%2FClasslikes%2F631315412" anchor-label="RtspSender" id="-673710707%2FClasslikes%2F631315412" data-filterable-set=":rtsp:dokkaHtmlPartial/release"></a>
          <div class="table-row" data-filterable-current=":rtsp:dokkaHtmlPartial/release" data-filterable-set=":rtsp:dokkaHtmlPartial/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-rtsp-sender/index.html"><span>Rtsp</span><wbr><span><span>Sender</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-673710707%2FClasslikes%2F631315412"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":rtsp:dokkaHtmlPartial/release"><div class="symbol monospace"><span class="token keyword">class </span><a href="-rtsp-sender/index.html">RtspSender</a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">connectChecker<span class="token operator">: </span><a href="../../common/com.pedro.common/-connect-checker/index.html">ConnectChecker</a><span class="token punctuation">, </span></span><span class="parameter ">commandsManager<span class="token operator">: </span><a href="../com.pedro.rtsp.rtsp.commands/-commands-manager/index.html">CommandsManager</a></span></span><span class="token punctuation">)</span> : <a href="../../common/com.pedro.common.base/-base-sender/index.html">BaseSender</a></div><div class="brief "><p class="paragraph">Created by pedro on 7/11/18.</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
    <div class="footer">
        <a href="#content" id="go-to-top-link" class="footer--button footer--button_go-to-top"></a>
        <span>© 2025 Copyright</span>
        <span class="pull-right">
            <span>Generated by </span>
            <a class="footer--link footer--link_external" href="https://github.com/Kotlin/dokka">
                <span>dokka</span>
            </a>
        </span>
    </div>
            </div>
        </div>
    </div>
</body>
</html>
