package com.blink.monitor.viewmodel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.aries.library.fast.manager.LoggerManager
import com.blink.monitor.BLRTCServerSession
import com.blink.monitor.KEY_IS_MUTED
import com.blink.monitor.KEY_IS_RECORDING
import com.blink.monitor.KEY_PHONE_POWER
import com.blink.monitor.KEY_REMOTE_CONNECT
import com.blink.monitor.KEY_REMOTE_POWER
import com.blink.monitor.KEY_SPORT_TYPE
import com.blink.monitor.KEY_TOPIC
import com.blink.monitor.*
import com.blink.monitor.OnMessageListener
import com.blink.monitor.TOPIC_MATCH_INFO
import com.blink.monitor.TOPIC_MUTE_SWITCH
import com.blink.monitor.TOPIC_PHONE_POWER
import com.blink.monitor.TOPIC_RECORD_SWITCH
import com.blink.monitor.TOPIC_REMOTE_INFO_STATE
import com.blink.monitor.TOPIC_SCOREBOARD_INFO
import com.blink.monitor.bean.ColorInfo
import com.blink.monitor.bean.ScoreInfo
import com.blink.monitor.fragment.NormalDialog
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import java.util.Locale
import java.util.concurrent.TimeUnit

class MonitorViewModel : ViewModel() {

    private var _batteryLivaData = MutableLiveData<Int>()
    val batteryLiveData = _batteryLivaData

    private var _remoteConnectLivaData = MutableLiveData<Int>()
    val remoteConnectLivaData = _remoteConnectLivaData

    private var _remotePowerLivaData = MutableLiveData<Int>()
    val remotePowerLivaData = _remotePowerLivaData

    private var _recordLivaData = MutableLiveData<Int>()
    val recordLivaData = _recordLivaData

    private var _stopCapture = MutableLiveData<Int>()
    val stopCapture = _stopCapture

    private var _muteLivaData = MutableLiveData<Int>()
    val muteLivaData = _muteLivaData

    private var _scoreType = MutableLiveData<Int>()
    val scoreType = _scoreType

    private var _elapsedTime = MutableStateFlow("00:00") // 初始时间
    val elapsedTime: StateFlow<String> = _elapsedTime

    private var startTime: Long = 0L
    private var isRunning = false // 计时标志

    val TAG = "MonitorViewModel"

    private val _isShowJoystick = MutableLiveData(false)
    val isShowJoystick = _isShowJoystick

    private val _isShowScorePanel = MutableLiveData(false)
    val isShowScorePanel: LiveData<Boolean> = _isShowScorePanel


    private val _isShowScoreCount = MutableLiveData(false)
    val isShowScoreCount: LiveData<Boolean> = _isShowScoreCount



    private val _deviceConnectedAbort = MutableLiveData<Boolean>()
    val deviceConnectedAbort = _deviceConnectedAbort

    var colorInfo = mutableMapOf<String, ColorInfo>()

    var scoreInfoLiveData: MutableLiveData<ScoreInfo> = MutableLiveData(ScoreInfo())

    var syncCoreData = MutableLiveData<Boolean>()


    init {
        BLRTCServerSession.run {

            onConnectListener = object : OnConnectListener {

                override fun onPeerConnectStatus(ipAddress: String, status: Int) {
                    _deviceConnectedAbort.postValue(status == -1)
                }

                override fun onPeerAddress(
                    ipAddress: String,
                    deviceName: String?,
                    deviceType: String,
                    timeStamp: Long
                ) {

                }
            }


            onMessageListener = object : OnMessageListener {

                override fun onDecodedFrame(pixelBuffer: ByteArray?) { //收到视频帧数据
                }

                override fun onPeerMessage(
                    javaMap: Map<String, Any>
                ) { //收到push端发送的消息，比如电量信息

                    LoggerManager.i(<EMAIL>, "message{}:$javaMap")
                    when (javaMap[KEY_TOPIC]) {
                        TOPIC_PHONE_POWER -> {
                            _batteryLivaData.postValue(javaMap[KEY_PHONE_POWER] as Int?)
                        }

                        TOPIC_REMOTE_INFO_STATE -> {
                            _remoteConnectLivaData.postValue(javaMap[KEY_REMOTE_CONNECT] as Int?)
                            _remotePowerLivaData.postValue(javaMap[KEY_REMOTE_POWER] as Int?)
                        }

                        TOPIC_MUTE_SWITCH -> {
                            _muteLivaData.postValue(javaMap[KEY_IS_MUTED] as Int?)
                        }

                        TOPIC_RECORD_SWITCH -> {
                            _recordLivaData.postValue(javaMap[KEY_IS_RECORDING] as Int?)
                        }

                        TOPIC_CAPTURED_SWITCH -> {
                            _stopCapture.postValue(javaMap[KEY_IS_CAPTURED] as Int?)
                        }

                        TOPIC_MATCH_INFO -> {
                            _scoreType.postValue(javaMap[KEY_SPORT_TYPE] as? Int)
                        }

                        TOPIC_SCOREBOARD_INFO -> {
                            val title = javaMap[KEY_TITLE] as String?
                            val team1Name = javaMap[KEY_HOME_NAME] as String?
                            val team2Name = javaMap[KEY_AWAY_NAME] as String?
                            val team1Color = javaMap[KEY_HOME_COLOR] as Int
                            val team2Color = javaMap[KEY_AWAY_COLOR] as Int
                            val hideScoreBoard = javaMap[KEY_HIDE_SCORE_BOARD] as Int
                            val team1Score = javaMap[KEY_HOME_SCORE] as Int
                            val team2Score = javaMap[KEY_AWAY_SCORE] as Int
                            val sect = javaMap[KEY_SECTION] as Int

                            val scoreInfo = ScoreInfo().apply {
                                this.title = title ?:""
                                this.homeName = team1Name ?: ""
                                this.awayName = team2Name ?: ""
                                this.homeColor = team1Color
                                this.awayColor = team2Color
                                this.hideScoreBoard =  hideScoreBoard
                                this.homeScore = team1Score
                                this.awayScore = team2Score
                                this.section = sect
                            }
                            scoreInfoLiveData.postValue(scoreInfo)
                            syncCoreData.postValue(true)

                        }
                    }
                }
            }
            sendSynMessage()
        }
    }

    fun toggleScoreCount(boolean: Boolean) {
        _isShowScoreCount.value = boolean
    }

    fun toggleScorePanel(boolean: Boolean) {
        _isShowScorePanel.value = boolean
    }

    fun toggleJoystick(boolean: Boolean) {
        _isShowJoystick.value = boolean
    }


    fun startTimer() {
        if (isRunning) return // 如果已经在计时，则不重复启动
        isRunning = true
        startTime = System.currentTimeMillis() // 设置开始时间
        // 启动协程更新时间
        viewModelScope.launch {
            while (isRunning) {
                val elapsed = System.currentTimeMillis() - startTime
                _elapsedTime.value = formatElapsedTime(elapsed)
                delay(1000) // 每秒更新
            }
        }
    }

    fun stopTimer() {
        isRunning = false // 设置标志位为 false
        _elapsedTime.value = "00:00"
    }

    private fun formatElapsedTime(milliseconds: Long): String {
        val hours = TimeUnit.MILLISECONDS.toHours(milliseconds)
        val minutes = TimeUnit.MILLISECONDS.toMinutes(milliseconds) % 60
        val seconds = TimeUnit.MILLISECONDS.toSeconds(milliseconds) % 60

        return when {
            hours > 0 -> String.format(
                Locale.getDefault(), "%02d:%02d:%02d", hours, minutes, seconds
            ) // HH:mm:ss
            else -> String.format(Locale.getDefault(), "%02d:%02d", minutes, seconds) // mm:ss
        }
    }

    override fun onCleared() {
        super.onCleared()
        BLRTCServerSession.run {
            onMessageListener = null
            destroySession()
        }
    }
}