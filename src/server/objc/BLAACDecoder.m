//
//  BLAACDecoder.m
//  LivePush
//
//  Created by ji peng on 2024/10/20.
//

#import "BLAACDecoder.h"
#import <AudioToolbox/AudioToolbox.h>

typedef struct {
    char *data;
    UInt32 size;
    UInt32 channelCount;
    AudioStreamPacketDescription packetDesc;
}AudioUserData;

@interface BLAACDecoder()
@property (nonatomic, strong) NSCondition *converterCond;
@property (nonatomic) dispatch_queue_t decoderQueue;
@property (nonatomic) dispatch_queue_t callbackQueue;

@property (nonatomic, strong) AudioAccConfig *config;

// 音频转换器对象
@property (nonatomic, unsafe_unretained) AudioConverterRef audioConverter;
@property (nonatomic) char *aacBuffer;
@property (nonatomic) UInt32 aacBufferSize;
@property (nonatomic) AudioStreamPacketDescription *packetDesc;

@end

@implementation BLAACDecoder

- (instancetype)init {
    return [self initWithConfig:nil];
}

- (instancetype)initWithConfig:(AudioAccConfig *)config {
    self = [super init];
    if (self) {
        // 音频编码配置
        _config = config;
        if (config == nil) {
            _config = AudioAccConfig.defaultConfig;
        }
        // 初始化队列
        _decoderQueue = dispatch_queue_create("AAC Decoder Queue", DISPATCH_QUEUE_SERIAL);
        _callbackQueue = dispatch_queue_create("AAC Decoder Callback Queue", DISPATCH_QUEUE_SERIAL);
        // 转换器对象/pcm缓冲区/acc缓冲区
        _audioConverter = NULL;
        _aacBufferSize = 0;
        _aacBuffer = NULL;
        AudioStreamPacketDescription desc = {0};
        _packetDesc = &desc;
        [self setupDecoder];
    }
    return self;
}

- (void)decodeAACData:(NSData *)data completionBlock:(void (^)(NSData *, NSError *))completionBlock {
    if (!_audioConverter) {return;}
    dispatch_async(_decoderQueue, ^{
        // 记录aac，作为参数传入解码回调函数
        AudioUserData userData = {0};
        userData.channelCount = (UInt32)self->_config.channelCount;
        userData.data = (char *)[data bytes];
        userData.size = (UInt32)data.length;
        userData.packetDesc.mDataByteSize = (UInt32)data.length;
        userData.packetDesc.mStartOffset = 0;
        userData.packetDesc.mVariableFramesInPacket = 0;
        
        // 输出大小和packet个数
        UInt32 pcmBufferSize = (UInt32)(2048 * self->_config.channelCount);
        UInt32 pcmDataPacketSize = 1024;
        // 创建临时容器pcm
        uint8_t *pcmBuffer = malloc(pcmBufferSize);
        memset(pcmBuffer, 0, pcmBufferSize);
        // 输出buffer
        AudioBufferList outAudioBufferList = {0};
        outAudioBufferList.mNumberBuffers = 1;
        outAudioBufferList.mBuffers[0].mNumberChannels = (uint32_t)self->_config.channelCount;
        outAudioBufferList.mBuffers[0].mDataByteSize = (UInt32)pcmBufferSize;
        outAudioBufferList.mBuffers[0].mData = pcmBuffer;
        
        // 输出描述
        AudioStreamPacketDescription outputPacketDesc = {0};
        // 配置填充函数，获取输出数据
        NSError *error = nil;
        OSStatus status = AudioConverterFillComplexBuffer(self->_audioConverter, &AudioDecoderConverterComplexInputDataProc, &userData, &pcmDataPacketSize, &outAudioBufferList, &outputPacketDesc);
        if (status != noErr) {
            error = [NSError errorWithDomain:NSOSStatusErrorDomain code:status userInfo:nil];
            return;
        }
        // 如果获取到数据
        if (outAudioBufferList.mBuffers[0].mDataByteSize > 0) {
            NSData *rawData = [NSData dataWithBytes:outAudioBufferList.mBuffers[0].mData length:outAudioBufferList.mBuffers[0].mDataByteSize];
            dispatch_async(self->_callbackQueue, ^{
                completionBlock(rawData, error);
            });
        }
        free(pcmBuffer);
    });
}

- (void)setupDecoder {
    // 输出参数pcm
    AudioStreamBasicDescription outputAudioDes = {0};
    outputAudioDes.mSampleRate = (Float64)_config.sampleRate; // 采样率
    outputAudioDes.mChannelsPerFrame = (UInt32)_config.channelCount;//输出声道数
    outputAudioDes.mFormatID = kAudioFormatLinearPCM; // 输出格式
    outputAudioDes.mFormatFlags = (kAudioFormatFlagIsSignedInteger | kAudioFormatFlagIsPacked); // 编码12
    outputAudioDes.mFramesPerPacket = 1; //每个Packet帧数
    outputAudioDes.mBitsPerChannel = 16; //数据帧中每个通道的采样位数
    outputAudioDes.mBytesPerFrame = outputAudioDes.mBitsPerChannel / 8 * outputAudioDes.mChannelsPerFrame; // 每一帧大小（采样率/8*声道数）
    outputAudioDes.mBytesPerPacket = outputAudioDes.mBytesPerFrame * outputAudioDes.mFramesPerPacket; // 每个Packet大小（帧大小 * 帧数）
    outputAudioDes.mReserved = 0; // 对其方式（0代表8字节对其）
    
    // 输入参数aac
    AudioStreamBasicDescription inputAudioDes = {0};
    inputAudioDes.mSampleRate = (Float64)_config.sampleRate;
    inputAudioDes.mFormatID = kAudioFormatMPEG4AAC;
    inputAudioDes.mFormatFlags = kMPEG4Object_AAC_LC;
    inputAudioDes.mFramesPerPacket = 1024;
    inputAudioDes.mChannelsPerFrame = (UInt32)_config.channelCount;
    
    // 填充输出相关信息
    UInt32 inDesSize = sizeof(inputAudioDes);
    AudioFormatGetProperty(kAudioFormatProperty_FormatInfo, 0, NULL, &inDesSize, &inputAudioDes);
    
    // 获取解码器的信息（只能传入software）
    AudioClassDescription *audioClassDesc = [self getAudioClassDescriptionWithType:outputAudioDes.mFormatID
                                                                  fromManufacturer:kAppleSoftwareAudioCodecManufacturer];
    
    /** 创建解码器
     参数1：输入音频格式描述
     参数2：输出音频格式描述
     参数3：class desc的数量
     参数4：class desc
     参数5：创建的解码器引用者
     */
    OSStatus status = AudioConverterNewSpecific(&inputAudioDes, &outputAudioDes, 1, audioClassDesc, &_audioConverter);
    if (status != noErr) {
        NSLog(@"error: 硬编码AAC创建失败，status=%d", (int)status);
        return;
    }
}

// 获取编码器类型描述
- (AudioClassDescription *)getAudioClassDescriptionWithType:(UInt32)type
                                           fromManufacturer:(UInt32)manufacturer
{
    static AudioClassDescription desc;

    UInt32 encoderSpecifier = type;
    OSStatus st;
    UInt32 size;
    /**
     参数1：编码器类型
     参数2：类型描述大小
     参数3：类型描述
     参数4：大小
     */
    st = AudioFormatGetPropertyInfo(kAudioFormatProperty_Encoders,
                                    sizeof(encoderSpecifier),
                                    &encoderSpecifier,
                                    &size);
    if (st) {
        NSLog(@"error getting audio format propery info: %d", (int)(st));
        return nil;
    }
    // 计算aac编码器个数
    unsigned int count = size / sizeof(AudioClassDescription);
    // 创建一个包含count个的编码器的数组
    AudioClassDescription descriptions[count];
    //将满足aac编码的编码器的信息写入数组
    st = AudioFormatGetProperty(kAudioFormatProperty_Encoders,
                                sizeof(encoderSpecifier),
                                &encoderSpecifier,
                                &size,
                                descriptions);
    if (st) {
        NSLog(@"error getting audio format propery: %d", (int)(st));
        return nil;
    }
    
    for (unsigned int i = 0; i < count; i++) {
        if ((type == descriptions[i].mSubType) &&
            (manufacturer == descriptions[i].mManufacturer)) {
            memcpy(&desc, &(descriptions[i]), sizeof(desc));
            return &desc;
        }
    }

    return nil;
}

// 解码回调函数
static OSStatus AudioDecoderConverterComplexInputDataProc(AudioConverterRef inAudioConverter,
                                                          UInt32 *ioNumberDataPackets,
                                                          AudioBufferList *ioData,
                                                          AudioStreamPacketDescription **outDataPacketDescription,
                                                          void *inUserData) {
    AudioUserData *audioDecoder = (AudioUserData *)inUserData;
    if (audioDecoder->size <= 0) {
        ioNumberDataPackets = 0;
        return -1;
    }
    
    // 填充数据
    *outDataPacketDescription = &audioDecoder->packetDesc;
    (*outDataPacketDescription)[0].mStartOffset = 0;
    (*outDataPacketDescription)[0].mDataByteSize = audioDecoder->size;
    (*outDataPacketDescription)[0].mVariableFramesInPacket = 0;
    
    ioData->mBuffers[0].mData = audioDecoder->data;
    ioData->mBuffers[0].mDataByteSize = audioDecoder->size;
    ioData->mBuffers[0].mNumberChannels = audioDecoder->channelCount;
    
    return noErr;
}
@end
