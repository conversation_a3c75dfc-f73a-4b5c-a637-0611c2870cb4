//
//  BLAVDecoder.h
//  Blink
//
//  Created by ji peng on 2024/10/16.
//

#import <Foundation/Foundation.h>
#import <AVFoundation/AVFoundation.h>
#import <VideoToolbox/VideoToolbox.h>


@protocol  BLAVDecoderDelegate <NSObject>

- (void)getDecodedFrame:(CVImageBufferRef)imageBuffer;

@end

@interface BLAVDecoder : NSObject

-(BOOL)initDecode;
-(void)decodeNalu:(uint8_t *)frame size:(uint32_t)frameSize;
-(void)stop;

@property (weak, nonatomic) id<BLAVDecoderDelegate> delegate;

@end
