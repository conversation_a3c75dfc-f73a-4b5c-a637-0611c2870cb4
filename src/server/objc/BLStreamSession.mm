//
//  BLStreamSession.m
//  Remote
//
//  Created by ji peng on 2024/10/28.
//

#import "BLStreamSession.h"
#import "BLAVDecoder.h"
#include "BLSRTServer.hpp"
#include "NSPMessage.hpp"
#include "BLNSPServer.hpp"
@interface BLStreamSession()<BLAVDecoderDelegate>{
    rtcsdk::BLNSPServer *nspServer;
    rtcsdk::BLSRTServer *srtServer;
    BLAVDecoder *decodec;
    dispatch_queue_t decodeQueue;
    NSString *lastPeerIp;
}
@end

@implementation BLStreamSession

- (instancetype)init{
    
    if (self = [super init]) {
        signal(SIGPIPE, SIG_IGN);
        decodeQueue = dispatch_queue_create("com.xbotgo.decodeQueue", DISPATCH_QUEUE_SERIAL);
        decodec = nil;
        nspServer = nullptr;
        srtServer = nullptr;
        lastPeerIp = nil;
    }
    return self;
}

-(void)startSession{
    NSLog(@"[rtcsdk] BLAVDecoder::startSession");
    if((nspServer!=nullptr) || (srtServer!=nullptr)){
        return;
    }

    if(nspServer==nullptr){
        nspServer = new rtcsdk::BLNSPServer();
    }
    if(srtServer==nullptr){
        srtServer = new rtcsdk::BLSRTServer();
    }
    if (nspServer->isConnected()) {
        return;
    }
    
    nspServer->onServerMessage([self](rtcsdk::BLNSPClient& client, int status){
        NSLog(@"RTCSDK: nspServer->onServerMessage status=%d",status);
        if(!self.delegate){
            return;
        }
        if((status==-1) || (status==1))
        {
            [self.delegate onPeerConnectStatus:&client Status:status];
        }
        else if(status==200)
        {
            [self.delegate onPeerDevicesRefresh:&client];
        }
    });
    nspServer->onPeerMessage([self](rtcsdk::BLNSPClient& client,rtcsdk::MQTTMessage& messag){
        [self.delegate onPeerMessage:&client Msg:&messag];
    });
    nspServer->start();

    if(decodec==nil){
        decodec =[[BLAVDecoder alloc] init];
        [decodec initDecode];
        decodec.delegate = self;
    }

    srtServer->onSRTServerMessage([self](uint8_t * frame, uint32_t frameSize){
        if(decodec)
        [decodec decodeNalu:frame size:frameSize];
    });
    srtServer->start();
}

- (void)stopSession{
    dispatch_async(decodeQueue, ^{
        if(self->srtServer){
            self->srtServer->stop();
            delete self->srtServer;
            self->srtServer = nullptr;
        }
        if(self->nspServer){
            self->nspServer->stop();
            delete self->nspServer;
            self->nspServer = nullptr;
        }
        if(self->decodec){
            [self->decodec stop];
            self->decodec = nil;
        }
        NSLog(@"[rtcsdk] BLStreamSession::stopSession");
    });
}

- (int)connectPeerSession:(NSString *)peerIp{
    if((peerIp == nil || peerIp.length == 0)){
        return -1;
    }
    if(!nspServer){
        return -1;
    }
    lastPeerIp = peerIp;
    return nspServer->sendConnectMessageToPeer(std::string([peerIp UTF8String]));
}

- (int)sendPeerMessage:(NSString*_Nullable)peerIp Topic:(NSString *)topic Data:(NSData*)payload{
    if(!nspServer){
        return -1;
    }
    return nspServer->sendPeerMessage(std::string([peerIp UTF8String]), [topic UTF8String], [payload length], [payload bytes]);
}

#pragma makr BLAVDecoderDelegate
- (void)getDecodedFrame:(CVImageBufferRef)imageBuffer{
    if(imageBuffer)
    {
        [self.delegate onDecodedFrame:imageBuffer];
    }
}


- (int)reconnectPeerSession
{
    NSLog(@"[rtcsdk] BLStreamSession::reconnectPeerSession");
    int ret = 0;
    dispatch_async(decodeQueue, ^{
        [self startSession];
        sleep(2);
        [self connectPeerSession:lastPeerIp];
    });
    return  ret;
}

@end
